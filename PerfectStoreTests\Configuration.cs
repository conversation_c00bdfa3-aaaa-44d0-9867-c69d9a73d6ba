﻿using Azure.Identity;
using Microsoft.Extensions.Configuration;

namespace PerfectStore.Tests
{
    public static class Configuration
    {
        public static IConfiguration GetConfiguration()
        {
            var env = System.Environment.GetEnvironmentVariable("BuildEnvironment");
            var configBuilder = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                .AddJsonFile($"appsettings.{env}.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables();

            var keyVaultEndpoint = Environment.GetEnvironmentVariable("KEYVAULT_ENDPOINT");
            if (!string.IsNullOrEmpty(keyVaultEndpoint))
            {
                //src: https://docs.microsoft.com/en-us/aspnet/core/security/key-vault-configuration?view=aspnetcore-6.0
                configBuilder.AddAzureKeyVault(
                    new <PERSON><PERSON>(keyVaultEndpoint),
                    new DefaultAzureCredential()
                );
            }

            return configBuilder.Build();
        }
    }
}
