//using Microsoft.Extensions.DependencyInjection;
//using PerfectEntityProcessor;
//using PerfectStore.Core.Services;
//using System;
//using System.Data;
//using System.Threading.Tasks;
//using static System.Runtime.InteropServices.JavaScript.JSType;

//// Not a mock test

//namespace PerfectStore.Tests
//{
//    /// <summary>
//    /// Integration tests for the Perfect Entity Triggered Processor using xUnit
//    /// </summary>
//    public class PerfectEntityTriggeredProcessorTests : IDisposable
//    {
//        private readonly ServiceProvider _serviceProvider;
//        private readonly PerfectEntityTriggered _triggeredProcessor;

//        /// <summary>
//        /// Constructor that runs before each test
//        /// </summary>
//        public PerfectEntityTriggeredProcessorTests()
//        {
//            // Set up environment variables for testing
//            Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3DebugWritable.vault.azure.net/");

//            // Get configuration
//            var configuration = Configuration.GetConfiguration();

//            // Set up dependency injection
//            IServiceCollection serviceCollection = new ServiceCollection();
//            PerfectEntityProcessor.Configurations.Dependencies.SetUp(
//                configuration,
//                serviceCollection
//            );

//            // Build service provider
//            _serviceProvider = serviceCollection.BuildServiceProvider();

//            // Get the Perfect Entity Service
//            var perfectEntityService = _serviceProvider.GetRequiredService<IPerfectEntityService>();

//            // Create the triggered processor
//            _triggeredProcessor = new PerfectEntityTriggered(perfectEntityService);
//        }

//        /// <summary>
//        /// Tests the triggered processor with a future date
//        /// </summary>
//        [Fact(DisplayName = "Process with future date should succeed")]
//        public async Task ProcessAsync_WithFutureDate_ShouldSucceed()
//        {
//            // Arrange
//            var futureDate = DateTime.Now.AddYears(1);

//            // Act
//            Func<Task> act = async () => await _triggeredProcessor.ProcessAsync(futureDate);

//            // Assert
//            await act.Should().NotThrowAsync("because processing a future date should not throw an exception");
//        }

//        /// <summary>
//        /// Tests the triggered processor with different dates
//        /// </summary>
//        /// <param name="years">Years to add to current date (can be negative)</param>
//        /// <param name="months">Months to add to current date (can be negative)</param>
//        /// <param name="days">Days to add to current date (can be negative)</param>
//        [Theory(DisplayName = "Process with various dates should succeed")]
//        [InlineData(1, 0, 0)]  // Future year
//        [InlineData(0, 1, 0)]  // Future month
//        [InlineData(0, 0, 1)]  // Future day
//        [InlineData(-1, 0, 0)] // Past year
//        [InlineData(0, -1, 0)] // Past month
//        [InlineData(0, 0, -1)] // Past day
//        public async Task ProcessAsync_WithVariousDates_ShouldSucceed(int years, int months, int days)
//        {
//            // Arrange
//            var testDate = DateTime.Now.AddYears(years).AddMonths(months).AddDays(days);

//            // Act
//            Func<Task> act = async () => await _triggeredProcessor.ProcessAsync(testDate);

//            // Assert
//            await act.Should().NotThrowAsync($"because processing a date ({testDate}) should not throw an exception");
//        }

//        /// <summary>
//        /// Tests the triggered processor with the first day of the month
//        /// </summary>
//        [Fact(DisplayName = "Process with first day of month should succeed")]
//        public async Task ProcessAsync_WithFirstDayOfMonth_ShouldSucceed()
//        {
//            // Arrange
//            var firstDayOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);

//            // Act
//            Func<Task> act = async () => await _triggeredProcessor.ProcessAsync(firstDayOfMonth);

//            // Assert
//            await act.Should().NotThrowAsync("because processing the first day of the month should not throw an exception");
//        }

//        /// <summary>
//        /// Tests the triggered processor with the last day of the month
//        /// </summary>
//        [Fact(DisplayName = "Process with last day of month should succeed")]
//        public async Task ProcessAsync_WithLastDayOfMonth_ShouldSucceed()
//        {
//            // Arrange
//            var lastDayOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month,
//                DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month));

//            // Act
//            Func<Task> act = async () => await _triggeredProcessor.ProcessAsync(lastDayOfMonth);

//            // Assert
//            await act.Should().NotThrowAsync("because processing the last day of the month should not throw an exception");
//        }

//        [Fact]
//        public async Task Test()
//        {
//            try
//            {
//                var date = new DateTime(2025, 06, 02);

//                await _triggeredProcessor.ProcessAsync(date);
//            }
//            catch (Exception ex)
//            {
//                Console.WriteLine(ex.Message);
//                throw;
//            }
//        }


//        /// <summary>
//        /// Cleanup method that runs after each test
//        /// </summary>
//        public void Dispose()
//        {
//            // Dispose of the service provider
//            _serviceProvider?.Dispose();
//        }
//    }
//}
