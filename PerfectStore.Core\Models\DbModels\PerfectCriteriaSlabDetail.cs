﻿using System.ComponentModel.DataAnnotations.Schema;

namespace PerfectStore.Core.Models.DbModels;

public class PerfectCriteriaSlabDetail
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public double SlabStartPercentage { get; set; } = 0;
    public double? SlabEndPercentage { get; set; }
    public int Sequence { get; set; } = 1;
    public double SlabWeightage { get; set; } = 0;
    public long CriteriaId { get; set; }
    public long RewardId { get; set; }
    public double RewardQuantity { get; set; } = 0;
    public SlabCalculationType SlabCalculationType { get; set; }
    [ForeignKey("CriteriaId")]
    public PerfectEntityRuleCriteria Criteria { get; set; }
}

public enum SlabCalculationType
{
    General = 0,
    InfiniteStep = 1,
    ValueSlab = 2,
    SlabMultiplier = 3
}