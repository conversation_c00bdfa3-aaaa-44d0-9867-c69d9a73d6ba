﻿using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;

namespace PerfectStore.DbStorage.DbContexts
{
    public class WritableMasterDbContext(DbContextOptions<WritableMasterDbContext> options)
        : DbContext(options)
    {
        public DbSet<TaskManagementTask> TaskManagementTasks { get; set; }

        public DbSet<TaskManagementSoldProductQuantity> TaskManagementSoldProductQuantities { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder) { }

        protected override void OnModelCreating(ModelBuilder modelBuilder) { }

        public override int SaveChanges()
        {
            throw new InvalidOperationException("use save changes async instead");
        }

        public override Task<int> SaveChangesAsync(
            CancellationToken cancellationToken = default(CancellationToken)
        )
        {
            return base.SaveChangesAsync(cancellationToken);
        }
    }
}
