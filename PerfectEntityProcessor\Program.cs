using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using PerfectEntityProcessor;
using PerfectEntityProcessor.Configurations;

var builder = new HostBuilder()
    .ConfigureAppConfiguration((config) =>
    {
        var env = Environment.GetEnvironmentVariable("BuildEnvironment");
        var keyVaultEndpoint = Environment.GetEnvironmentVariable("KEYVAULT_ENDPOINT");
        if (!string.IsNullOrEmpty(keyVaultEndpoint))
        {
            config.AddAzureKeyVault(keyVaultEndpoint);
        }

        config.AddEnvironmentVariables();
    })
    .ConfigureWebHostDefaults(webBuilder =>
    {
        webBuilder.Configure(app =>
        {
            app.UseRouting();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapGet("/", async context =>
                {
                    await context.Response.WriteAsync("Hello, World!");
                });
            });
        });
        // Add the following line:
        webBuilder.UseSentry(o =>
        {
            o.Dsn = "http://<EMAIL>:8000/5";
            o.Debug = false; // Disable Sentry debugging logs
            o.TracesSampleRate = 1.0; // Capture 100% of transactions
            o.Environment = Environment.GetEnvironmentVariable("BuildEnvironment") ?? "Production";
        });
    })
    .ConfigureServices((context, services) =>
    {
        Dependencies.SetUp(context.Configuration, services);
    })
    .UseConsoleLifetime();

var host = builder.Build();

using (host)
{
    try
    {
        var dateTime = DateTime.UtcNow;
        await host.Services.GetRequiredService<PerfectEntityTriggered>().ProcessAsync(dateTime);
    }
    catch (Exception ex)
    {
        SentrySdk.CaptureException(ex);
        throw;
    }
}