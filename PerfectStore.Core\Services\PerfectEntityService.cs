using System.Text.Json;
using Cronos;
using Libraries.CommonEnums;
using Libraries.CommonModels;
using PerfectStore.Core.Helpers;
using PerfectStore.Core.Helpers.QueueHandlers;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.Core.Utils.Helpers;

namespace PerfectStore.Core.Services;

public interface IPerfectEntityService
{
    Task ProcessAsync(DateTime startTime);
}

public class PerfectEntityService(
        ICompanySettingRepository companySettingRepository,
        IPerfectEntityRepository perfectEntityRepository,
        ILocationRepository locationRepository,
        PerfectEntityRuleQueueHandler perfectEntityRuleQueueHandler,
        IEmployeeRepository employeeRepository,
        IPositionCodeEntityMappingRepository positionCodeEntityMappingRepository,
        IDistributorRepository distributorRepository,
        IMTDService mtdService
    ) : IPerfectEntityService
{

    public async Task ProcessAsync(DateTime date)
    {
        var companyIds = await companySettingRepository.GetAllCompanyIdsUsingPerfectEntity();
        foreach (var companyId in companyIds)
        {
            var companySettings = CompanySettings.Initialize(
                companySettingRepository.GetSettings(companyId)
            );
            var yearStartMonth = companySettings.YearStartMonth;
            var mtd = await mtdService.GetDates(companyId, date, yearStartMonth, true);
            var timeZoneOffset = companySettings.TimeZoneOffset;
            var todayDate = date.Add(timeZoneOffset).Date;
            var startDate = mtd.MTD.StartDate;
            var endDate = mtd.MTD.EndDate;
            var monthNumber = mtd.MTD.MonthNumber;

            var activeRules = await perfectEntityRepository.GetActivePerfectEntityRules(
                companyId,
                date.Date
            );

            foreach (var rule in activeRules)
            {
                var filterConstraints = rule.FilterConstraintDetails;

                if (filterConstraints == null)
                {
                    return;
                }

                List<long> entityIds = [];

                switch (filterConstraints.EntityType)
                {
                    case FilterConstraintEntityType.Outlet:
                        entityIds = await ProcessOutlets(filterConstraints, companyId);
                        break;
                    case FilterConstraintEntityType.User:
                        entityIds = await ProcessUsers(filterConstraints, companyId);
                        break;
                    case FilterConstraintEntityType.Distributor:
                        entityIds = await ProcessDistributors(filterConstraints, companyId);
                        break;
                }

                if (entityIds.Count == 0)
                {
                    continue;
                }

                List<long>? productDivisionIds = string.IsNullOrEmpty(rule.ProductDivisionIds)
                    ? []
                    : JsonSerializer.Deserialize<List<long>>(rule.ProductDivisionIds);

                foreach (var criteria in rule.Criterias)
                {
                    var cron = criteria.Cron ?? "0 0 * * *"; // Default to daily if cron is null
                    if (IsCronPassed(date, cron, companyId, timeZoneOffset))
                    {
                        await EnqueueEntityRuleData(
                            companyId,
                            rule,
                            filterConstraints,
                            criteria.Id,
                            date,
                            todayDate,
                            startDate,
                            endDate,
                            monthNumber,
                            entityIds,
                            productDivisionIds);
                    }
                }
            }
        }
    }

    private async Task EnqueueEntityRuleData(
            long companyId,
            PerfectEntityRule rule,
            FilterConstraintDetail filterConstraints,
            long criteriaId,
            DateTime date,
            DateTime todayDate,
            DateTime startDate,
            DateTime endDate,
            int monthNumber,
            List<long> entityIds,
            List<long>? productDivisionIds)
    {
        foreach (var entityId in entityIds)
        {
            if (productDivisionIds != null && productDivisionIds.Count > 0)
            {
                foreach (var productDivisionId in productDivisionIds)
                {
                    await perfectEntityRuleQueueHandler.AddToQueue(new PerfectEntityRuleQueueData
                    {
                        CompanyId = companyId,
                        RuleId = rule.Id,
                        RuleType = rule.RuleType,
                        CriteriaId = criteriaId,
                        Date = date,
                        EntityType = filterConstraints.EntityType,
                        QualifierIds = rule.QualifierIds ?? string.Empty,
                        QualifierRelation = rule.QualifierRelation ?? string.Empty,
                        TodayDate = todayDate,
                        StartDate = startDate,
                        EndDate = endDate,
                        MonthNumber = monthNumber,
                        EntityId = entityId,
                        ProductDivisionId = productDivisionId
                    });
                }
            }
            else
            {
                await perfectEntityRuleQueueHandler.AddToQueue(new PerfectEntityRuleQueueData
                {
                    CompanyId = companyId,
                    RuleId = rule.Id,
                    RuleType = rule.RuleType,
                    CriteriaId = criteriaId,
                    Date = date,
                    EntityType = filterConstraints.EntityType,
                    QualifierIds = rule.QualifierIds ?? string.Empty,
                    QualifierRelation = rule.QualifierRelation ?? string.Empty,
                    TodayDate = todayDate,
                    StartDate = startDate,
                    EndDate = endDate,
                    MonthNumber = monthNumber,
                    EntityId = entityId,
                    ProductDivisionId = null
                });
            }
        }
    }


    private static bool IsCronPassed(DateTime date, string cron, long companyId, TimeSpan timeZoneOffset)
    {
        if (cron != null)
        {
            var currentTime = date.Add(timeZoneOffset);
            // Adjust the start and end dates to be 15 minutes before and after the current time
            var startDate = new DateTime(currentTime.AddMinutes(-15).Ticks, DateTimeKind.Utc); 
            var endDate = new DateTime(currentTime.AddMinutes(15).Ticks, DateTimeKind.Utc);
            var expression = CronExpression.Parse(cron);
            var occurrences = expression.GetOccurrences(startDate, endDate, true, true);
            return occurrences.ToList().Count > 0;
        }

        return true;
    }


    private async Task<List<long>> ProcessOutlets(
      FilterConstraintDetail filterConstraints,
      long companyId)
    {
        var outletUniverse = await locationRepository.GetOutletsForCompany(companyId);
        if (string.IsNullOrEmpty(filterConstraints.ConstraintJson))
        {
            return outletUniverse.Select(s => s.Id).Distinct().ToList();
        }
        var constraintList = JsonSerializer.Deserialize<FilterConstraintModel>(
            filterConstraints.ConstraintJson
        );

        var filteredOutletList = await FilterOutletBasedOnLocConstraints(constraintList, outletUniverse, companyId);
        return filteredOutletList.Select(s => s.Id).Distinct().ToList();

    }

    private async Task<List<Models.Location>> FilterOutletBasedOnLocConstraints(
       FilterConstraintModel constraintDetail,
       List<Models.Location> outlets,
       long companyId
   )
    {
        outlets = ApplyGeographyConstraints(outlets, constraintDetail.GeographyConstraints);
        outlets = ApplyOutletCohortConstraints(outlets, constraintDetail.OutletCohortConstraints);
        outlets = await ApplyDistributorConstraints(outlets, constraintDetail.DistributorConstraints, companyId);
        outlets = await ApplyDistributorIdFilter(outlets, constraintDetail.Distributor, companyId);
        return outlets;
    }

    private static List<Models.Location> ApplyGeographyConstraints(List<Models.Location> outlets, GeographyConstraintModel geographyConstraints)
    {
        if (geographyConstraints == null) return outlets;

        if (geographyConstraints.RegionIds != null && geographyConstraints.RegionIds.Any())
        {
            outlets = outlets.Where(o => o.RegionId.HasValue && geographyConstraints.RegionIds.Contains(o.RegionId.Value)).ToList();
        }

        if (geographyConstraints.ZoneIds != null && geographyConstraints.ZoneIds.Any())
        {
            outlets = outlets.Where(o => o.ZoneId.HasValue && geographyConstraints.ZoneIds.Contains(o.ZoneId.Value)).ToList();
        }

        return outlets;
    }

    private static List<Models.Location> ApplyOutletCohortConstraints(List<Models.Location> outlets, OutletCohortConstraints cohortConstraints)
    {
        if (cohortConstraints == null) return outlets;

        if (cohortConstraints.Channels?.Any() == true)
        {
            var channelValues = cohortConstraints.Channels.Select(c => Enum.Parse<OutletChannel>(c)).ToHashSet();
            outlets = outlets.Where(o => channelValues.Contains(o.OutletChannel)).ToList();
        }

        if (cohortConstraints.Segmentations?.Any() == true)
        {
            var segmentationValues = cohortConstraints.Segmentations.Select(s => Enum.Parse<OutletSegmentation>(s)).ToHashSet();
            outlets = outlets.Where(o => segmentationValues.Contains(o.Segmentation)).ToList();
        }

        if (cohortConstraints.ShopTypes?.Any() == true)
        {
            outlets = outlets.Where(o => o.ShopTypeId.HasValue && cohortConstraints.ShopTypes.Contains(o.ShopTypeId.Value)).ToList();
        }

        if (!string.IsNullOrEmpty(cohortConstraints.IsFocused))
        {
            var isFocusedBoolean = bool.Parse(cohortConstraints.IsFocused);
            outlets = outlets.Where(o => o.IsFocused == isFocusedBoolean).ToList();
        }

        if (cohortConstraints.CustomTag?.Any() == true)
        {
            outlets = outlets.Where(o => o.CustomTags != null && cohortConstraints.CustomTag.Any(tag => o.CustomTags.Contains(tag))).ToList();
        }

        if (!string.IsNullOrEmpty(cohortConstraints.AttributeText1))
        {
            outlets = outlets.Where(o => o.AttributeText1 == cohortConstraints.AttributeText1).ToList();
        }

        if (cohortConstraints.AttributeNumber1.HasValue)
        {
            outlets = outlets.Where(o => o.AttributeNumber1 == cohortConstraints.AttributeNumber1).ToList();
        }

        if (cohortConstraints.AttributeBoolean1.HasValue)
        {
            outlets = outlets.Where(o => o.AttributeBoolean1.HasValue && o.AttributeBoolean1.Value).ToList();
        }

        return outlets;
    }

    private async Task<List<Models.Location>> ApplyDistributorConstraints(List<Models.Location> outlets, DistributorConstraints distributorConstraints, long companyId)
    {
        if (distributorConstraints == null) return outlets;

        // Get distributors from distributor constraints
        var distributorIds = await distributorRepository.GetDistributorIdsForChannelAndSegmentationIds(
            distributorConstraints.Channels.Select(s => long.Parse(s)).ToList(),
            distributorConstraints.Segmentations.Select(s => long.Parse(s)).ToList(),
            companyId);

        return await ApplyDistributorIdFilter(outlets, new DistributorModel { DistributorIds = distributorIds }, companyId);
    }

    private async Task<List<Models.Location>> ApplyDistributorIdFilter(List<Models.Location> outlets, DistributorModel distributor, long companyId)
    {
        if (distributor?.DistributorIds == null || !distributor.DistributorIds.Any()) return outlets;

        var regionIdsForDistributors = await distributorRepository.GetRegionIdsForDistributorIds(distributor.DistributorIds, companyId);

        return ApplyGeographyConstraints(outlets, new GeographyConstraintModel { RegionIds = regionIdsForDistributors });
    }

    private async Task<List<long>> ProcessUsers(
        FilterConstraintDetail filterConstraints,
        long companyId
    )
    {
        var userFilterConstraintJson = JsonSerializer.Deserialize<UserFilterConstraintJson>(
            filterConstraints.ConstraintJson
        );
        if (userFilterConstraintJson == null)
            return [];

        var filteredUserIds = await FilterUsersBasedOnUserConstraints(
            userFilterConstraintJson,
            companyId,
            filterConstraints.UserPlatform ?? UserPlatform.UserApp
        );

        return filteredUserIds;
    }

    private async Task<List<long>> FilterUsersBasedOnUserConstraints(
        UserFilterConstraintJson userFilterConstraintJson,
        long companyId,
        UserPlatform userPlatform
    )
    {
        var userIds = new List<long>();

        switch (userPlatform)
        {
            case UserPlatform.UserApp or UserPlatform.AnalyticApp
                when userFilterConstraintJson is { UserInfos: not null, EmployeeRanks: not null }:
                {
                    foreach (var userInfo in userFilterConstraintJson.UserInfos)
                    {
                        var userRole = userInfo.Rank switch
                        {
                            EmployeeRank.ESM => PortalUserRole.ClientEmployee,
                            EmployeeRank.ASM => PortalUserRole.AreaSalesManager,
                            EmployeeRank.RSM => PortalUserRole.RegionalSalesManager,
                            EmployeeRank.ZSM => PortalUserRole.ZonalSalesManager,
                            EmployeeRank.NSM => PortalUserRole.NationalSalesManager,
                            _ => PortalUserRole.GlobalSalesManager,
                        };

                        EmployeeType? employeeType = null;
                        var employeeTypes = userFilterConstraintJson.EmployeeTypes;
                        if (employeeTypes != null)
                        {
                            if (employeeTypes.Count == 1 && employeeTypes[1] == EmployeeType.SR)
                            {
                                employeeType = EmployeeType.SR;
                            }
                            else if (employeeTypes.Count == 1 && employeeTypes[1] == EmployeeType.DSR)
                            {
                                employeeType = EmployeeType.DSR;
                            }
                        }

                        var users = await employeeRepository.GetFieldUserIdsUnderManagerModel(
                            companyId,
                            userRole,
                            userInfo.Id,
                            employeeType
                        );

                        var employeeRanks = userFilterConstraintJson.EmployeeRanks;

                        var ids = users
                            .Where(s => employeeRanks.Contains(s.UserRank))
                            .Select(s => s.Id)
                            .ToList();

                        userIds.AddRange(ids);
                    }
                    break;
                }

            case UserPlatform.UserApp or UserPlatform.AnalyticApp
                when userFilterConstraintJson is { PositionInfos: not null, PositionCodeLevels: not null }:
                {
                    foreach (var positionInfo in userFilterConstraintJson.PositionInfos)
                    {
                        var positionCodeLevelsList = userFilterConstraintJson.PositionCodeLevels;
                        var employeeType = userFilterConstraintJson.EmployeeTypes;

                        // Get all the positions under position codes in cohort
                        var hierarchy =
                            await positionCodeEntityMappingRepository.GetAllPositionCodesUnderUser(
                                companyId,
                                positionInfo.PositionId,
                                false,
                                true
                            );

                        var filteredEntityIds = hierarchy
                            .Where(p => positionCodeLevelsList.Contains(p.PositionCodeLevel))
                            .Select(p => p.EntityId)
                            .Distinct()
                            .ToList();

                        if (positionCodeLevelsList.Contains(positionInfo.Level))
                        {
                            var parentPositionId =
                                await positionCodeEntityMappingRepository.GetPositionUsers(
                                    companyId,
                                    positionInfo.PositionId
                                );
                            filteredEntityIds.AddRange(parentPositionId.Select(item => item.EntityId));
                        }

                        List<EmployeeMinWithType> employees;

                        if (userPlatform == UserPlatform.AnalyticApp)
                        {
                            employees = await employeeRepository.GetAllEmployees(companyId);
                        }
                        else
                        {
                            employees = await employeeRepository.GetAllEmployeesSRAndDSR(companyId);
                        }

                        var ids = employees
                            .Where(e => filteredEntityIds.Contains(e.Id) &&
                                        (userPlatform == UserPlatform.AnalyticApp || employeeType.Contains(e.UserType)))
                            .Select(e => e.Id)
                            .ToList();

                        userIds.AddRange(ids);
                    }
                    break;
                }
        }

        return userIds;
    }

    private async Task<List<long>> ProcessDistributors(FilterConstraintDetail filterConstraints, long companyId)
    {
        var distributorUniverse = await distributorRepository.GetAllDistributorForCompany(companyId);
        if (string.IsNullOrEmpty(filterConstraints.ConstraintJson))
        {
            return distributorUniverse.Select(s => s.Id).Distinct().ToList();
        }
        var constraintList = JsonSerializer.Deserialize<FilterConstraintModel>(filterConstraints.ConstraintJson);

        var filteredDistributorList = FilterDistributorsBasedOnDistributorConstraints(constraintList, distributorUniverse, companyId);
        return filteredDistributorList.Select(s => s.Id).Distinct().ToList();
    }

    private static List<Distributor> FilterDistributorsBasedOnDistributorConstraints(FilterConstraintModel constraintModel,
        List<Distributor> distributorUniverse, long companyId)
    {
        if (constraintModel.GeographyConstraints != null)
        {
            if (constraintModel.GeographyConstraints.RegionIds != null && constraintModel.GeographyConstraints.RegionIds.Any())
            {
                distributorUniverse = distributorUniverse
                    .Where(d => d.RegionId.HasValue && constraintModel.GeographyConstraints.RegionIds.Contains(d.RegionId.Value))
                    .ToList();
            }

            if (constraintModel.GeographyConstraints.ZoneIds != null && constraintModel.GeographyConstraints.ZoneIds.Any())
            {
                distributorUniverse = distributorUniverse
                    .Where(d => d.ZoneId.HasValue && constraintModel.GeographyConstraints.ZoneIds.Contains(d.ZoneId.Value))
                    .ToList();
            }
        }

        if (constraintModel.DistributorConstraints != null)
        {
            if (constraintModel.DistributorConstraints.Channels != null && constraintModel.DistributorConstraints.Channels.Any())
            {
                var intDistributorChannels = constraintModel.DistributorConstraints.Channels
                    .Select(c => long.Parse(c))
                    .ToHashSet();

                distributorUniverse = distributorUniverse.Where(s => s.DistributorChannelId.HasValue &&
                    intDistributorChannels.Contains(s.DistributorChannelId.Value)).ToList();
            }

            if (constraintModel.DistributorConstraints.Segmentations != null && constraintModel.DistributorConstraints.Segmentations.Any())
            {
                var intDistributorSegmentations = constraintModel.DistributorConstraints.Segmentations
                    .Select(c => long.Parse(c))
                    .ToHashSet();

                distributorUniverse = distributorUniverse.Where(s => s.DistributorSegmentationId.HasValue &&
                    intDistributorSegmentations.Contains(s.DistributorSegmentationId.Value)).ToList();
            }
        }

        if (constraintModel.Distributor != null && constraintModel.Distributor.DistributorIds != null && constraintModel.Distributor.DistributorIds.Any())
        {
            var distributorIds = constraintModel.Distributor.DistributorIds;
            distributorUniverse = distributorUniverse.Where(s => distributorIds.Contains(s.Id)).ToList();
        }

        return distributorUniverse;
    }

}
