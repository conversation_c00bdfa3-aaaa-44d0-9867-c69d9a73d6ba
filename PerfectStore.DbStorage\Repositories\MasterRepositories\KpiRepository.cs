﻿using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.MasterRepositories;

public class KpiRepository(MasterDbContext masterDbContext) : IKpiRepository
{
    public async Task<List<Kpi>> GetKpiListByIds(List<long> kpiIds, long companyId)
    {
        var query =
            from kpi in masterDbContext.KPIs.Where(k => kpiIds.Contains(k.Id) && !k.IsDeactivated)
            from ckpi in masterDbContext
                .CompanyKPIs.Where(k =>
                    k.KpiId == kpi.Id && !k.IsDeactivated && k.CompanyId == companyId
                )
                .DefaultIfEmpty()
            select new Kpi
            {
                Id = kpi.Id,
                Frequency = ckpi != null ? ckpi.Frequency : kpi.Frequency,
                UserType = ckpi != null ? ckpi.UserType : kpi.UserType,
                Objective = ckpi != null ? ckpi.Objective : kpi.Objective,
                Measure = ckpi != null ? ckpi.Measure : kpi.Measure,
                IsQualifier = ckpi != null ? ckpi.IsQualifier : kpi.IsQualifier,
                Description = ckpi != null ? ckpi.Description : kpi.Description,
                KPIType = ckpi != null ? ckpi.KPIType : kpi.KPIType,
                Name = ckpi != null ? ckpi.Name : kpi.Name,
                UIName = ckpi != null ? ckpi.UIName : kpi.UIName,
                Sequence = ckpi != null ? ckpi.Sequence : kpi.Sequence,
                Calculation = ckpi != null ? ckpi.Calculation : kpi.Calculation,
                TargetSQLQuery = ckpi != null ? ckpi.TargetSQLQuery : kpi.TargetSQLQuery,
            };
        return await query.ToListAsync();
    }

    public async Task<Kpi> GetKpiById(long kpiId, long companyId)
    {
        var query =
            from kpi in masterDbContext.KPIs.Where(k => k.Id == kpiId && !k.IsDeactivated)
            from ckpi in masterDbContext
                .CompanyKPIs.Where(k =>
                    k.KpiId == kpiId && !k.IsDeactivated && k.CompanyId == companyId
                )
                .Where(x => x.KpiId == kpi.Id)
                .DefaultIfEmpty()
            select new Kpi
            {
                Id = kpi.Id,
                Frequency = ckpi != null ? ckpi.Frequency : kpi.Frequency,
                UserType = ckpi != null ? ckpi.UserType : kpi.UserType,
                Objective = ckpi != null ? ckpi.Objective : kpi.Objective,
                Measure = ckpi != null ? ckpi.Measure : kpi.Measure,
                IsQualifier = ckpi != null ? ckpi.IsQualifier : kpi.IsQualifier,
                Description = ckpi != null ? ckpi.Description : kpi.Description,
                KPIType = ckpi != null ? ckpi.KPIType : kpi.KPIType,
                Name = ckpi != null ? ckpi.Name : kpi.Name,
                UIName = ckpi != null ? ckpi.UIName : kpi.UIName,
                Sequence = ckpi != null ? ckpi.Sequence : kpi.Sequence,
                Calculation = ckpi != null ? ckpi.Calculation : kpi.Calculation,
                SQLQuery = ckpi != null ? ckpi.SQLQuery : kpi.SQLQuery,
                Relation = ckpi != null ? ckpi.Relation : kpi.Relation,
                MasterSQLQuery = ckpi != null ? ckpi.MasterSQLQuery : kpi.MasterSQLQuery,
                TargetSQLQuery = ckpi != null ? ckpi.TargetSQLQuery : kpi.TargetSQLQuery,
                TransactionSQLQuery =
                    ckpi != null ? ckpi.TransactionSQLQuery : kpi.TransactionSQLQuery,
                WeekOfDay = ckpi != null ? ckpi.WeekOfDay : kpi.WeekOfDay,
            };

        return await query.FirstAsync();
    }
}
