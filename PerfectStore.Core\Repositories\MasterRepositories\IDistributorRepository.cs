﻿using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Models.DbModels.TransactionDbModels;

namespace PerfectStore.Core.Repositories.MasterRepositories;

public interface IDistributorRepository
{
    Task<List<long>> GetRegionIdsForDistributorIds(List<long> distributorIds, long companyId);
    Task<List<long>> GetDistributorIdsForChannelAndSegmentationIds(List<long> channelIds, List<long> segmentationIds, long companyId);
    Task<List<Distributor>> GetAllDistributorForCompany(long companyId);
    Task<List<(long OutletId, long BeatId, long DistributorId)>> GetBeatAndDistributorFromOutlets(long companyId, List<long> outletIds);
    Task<List<DistributorStock>> GetDistributorStocks(long companyId, List<long> distributorIds, DateTime date);
    Task<List<StockistStock>> GetDistributorStocksFromMaster(long companyId, List<long> distributorIds);
}
