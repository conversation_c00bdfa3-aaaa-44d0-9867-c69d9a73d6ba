# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS base
WORKDIR /app
ENV DOTNET_RUNNING_IN_CONTAINER=true
RUN apk add --no-cache icu-libs
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false

# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["PerfectEntityContinuousProcessor/PerfectEntityContinuousProcessor.csproj", "PerfectEntityContinuousProcessor/"]
COPY ["Lite_Library/Library.ConnectionStringParsor/Library.ConnectionStringParsor.csproj", "Lite_Library/Library.ConnectionStringParsor/"]
COPY ["Lite_Library/Library.SqlHelper/Library.SqlHelper.csproj", "Lite_Library/Library.SqlHelper/"]
COPY ["Lite_Library/Libraries.CommonEnums/Libraries.CommonEnums.csproj", "Lite_Library/Libraries.CommonEnums/"]
COPY ["Lite_Library/Library.CommonHelpers/Library.CommonHelpers.csproj", "Lite_Library/Library.CommonHelpers/"]
COPY ["Lite_Library/Library.DateTimeHelpers/Library.DateTimeHelpers.csproj", "Lite_Library/Library.DateTimeHelpers/"]
COPY ["PerfectStore.DbStorage/PerfectStore.DbStorage.csproj", "PerfectStore.DbStorage/"]
COPY ["PerfectStore.Core/PerfectStore.Core.csproj", "PerfectStore.Core/"]
COPY ["Lite_Library/Libraries.CommonModels/Libraries.CommonModels.csproj", "Lite_Library/Libraries.CommonModels/"]
COPY ["Lite_Library/Library.FaExceptions/Library.FaExceptions.csproj", "Lite_Library/Library.FaExceptions/"]
COPY ["Lite_Library/Library.Infrastructure/Library.Infrastructure.csproj", "Lite_Library/Library.Infrastructure/"]
COPY ["Lite_Library/Library.NumberSystem/Library.NumberSystem.csproj", "Lite_Library/Library.NumberSystem/"]
RUN dotnet restore "./PerfectEntityContinuousProcessor/PerfectEntityContinuousProcessor.csproj"
COPY . .
WORKDIR "/src/PerfectEntityContinuousProcessor"
RUN dotnet build "./PerfectEntityContinuousProcessor.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./PerfectEntityContinuousProcessor.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "PerfectEntityContinuousProcessor.dll"]