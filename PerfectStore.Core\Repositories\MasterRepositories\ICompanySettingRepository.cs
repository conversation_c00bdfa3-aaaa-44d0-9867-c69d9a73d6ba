﻿namespace PerfectStore.Core.Repositories.MasterRepositories
{
    public interface ICompanySettingRepository
    {
        Dictionary<string, object> GetSettings(long companyId);

        Task<List<long>> GetAllCompanyIdsUsingPerfectStore();

        Task<int> GetYearStartMonth(long companyId);

        Task<TimeSpan> GetTimeZoneOffset(long companyId);

        Task<List<long>> GetAllCompanyIdsUsingPerfectEntity();
    }
}
