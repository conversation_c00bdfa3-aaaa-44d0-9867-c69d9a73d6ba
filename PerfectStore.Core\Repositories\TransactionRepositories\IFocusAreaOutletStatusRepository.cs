﻿using PerfectStore.Core.Models.DbModels;

namespace PerfectStore.Core.Repositories.TransactionRepositories
{
    public interface IFocusAreaOutletStatusRepository
    {
        Task UpsertFocusAreaOutletStatus(
            List<FocusAreaOutletStatus> newRecords,
            long companyId,
            DateTime startDate,
            DateTime endDate
        );

        Task<List<FocusAreaOutletStatus>> GetFocusAreaOutletStatus(
            long companyId,
            List<long> focusAreaIds,
            DateTime startDate,
            DateTime endDate
        );
    }
}
