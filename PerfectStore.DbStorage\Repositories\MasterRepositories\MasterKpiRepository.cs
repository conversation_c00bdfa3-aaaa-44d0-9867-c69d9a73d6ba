﻿using Library.CommonHelpers;
using Microsoft.IdentityModel.Tokens;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.DbStorage.DbContexts;
using System.Data.SqlClient;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace PerfectStore.DbStorage.Repositories.MasterRepositories;

public class MasterKpiRepository(MasterDbSqlDataReader masterDbSqlDataReader) : IMasterKpiRepository
{
    public async Task<string?> GetKPIAchievedTarget(string query,
        long companyId,
        long entityId,
        FilterConstraintEntityType entityType,
        long? productDivisionId,
        DateTime startDate,
        DateTime endDate,
        int monthnumber)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return null;
        }

        var parameters = new List<SqlParameter>();
        var paramsInQuery = Regex.Matches(query, @"@\b\S+?\b");

        foreach (Match p in paramsInQuery)
        {
            string? value = null;
            switch (p.Value.ToLower())
            {
                case "@companyid":
                    value = companyId.ToString();
                    break;
                case "@esmid":
                    value = entityType == FilterConstraintEntityType.User ? entityId.ToString() : null;
                    break;
                case "@startdatekey":
                    value = startDate.ToString("yyyyMMdd");
                    break;
                case "@enddatekey":
                    value = endDate.ToString("yyyyMMdd");
                    break;
                case "@productDivisionId":
                    value = productDivisionId?.ToString();
                    break;
                case "@id":
                    value = (entityType == FilterConstraintEntityType.Outlet || entityType == FilterConstraintEntityType.Distributor)
                            ? entityId.ToString()
                            : null;
                    break;
                case "@monthnumber":
                    value = monthnumber.ToString();
                    break;
            }

            if (value != null)
                parameters.Add(new SqlParameter(p.Value.ToLower(), value));
        }


        return await masterDbSqlDataReader.GetSingleResultOfQuery(query, parameters);
    }

    public async Task<string?> GetMasterMetricValue(
        string query,
        long companyId,
        long entityId,
        FilterConstraintEntityType entityType,
        long? productDivisionId,
        DateTime startDate,
        DateTime endDate,
        int monthNumber,
        string? parameterValue = null
)
    {
        var replaceSqlQuery = ParameterizedSqlQuery.GetReplaceSqlQueryForPattern(query, @"\[\[(\w+)\]\]");
        var paramsInQuery = ParameterizedSqlQuery.GetParametersInSqlQuery(replaceSqlQuery, @"@\b\S+?\b");

        var parameters = new List<SqlParameter>();
        var parameterListDict = new Dictionary<string, List<long>>();
        if (!string.IsNullOrEmpty(parameterValue))
        {
            var parameterList = JsonSerializer.Deserialize<List<ParameterValue>>(parameterValue);
            parameterListDict = parameterList.ToDictionary(s => s.ParameterName, s => s.ParameterValues);
        }

        foreach (Match p in paramsInQuery)
        {
            string? value = null;
            var paramName = p.Value.ToLower();
            switch (paramName)
            {
                case "@companyid":
                    value = companyId.ToString();
                    break;
                case "@esmid":
                    value = entityType == FilterConstraintEntityType.User ? entityId.ToString() : null;
                    break;
                case "@startdate":
                    value = startDate.ToString("yyyyMMdd");
                    break;
                case "@enddate":
                    value = endDate.ToString("yyyyMMdd");
                    break;
                case "@productDivisionId":
                    value = productDivisionId?.ToString();
                    break;
                case "@id":
                    value = (entityType == FilterConstraintEntityType.Outlet || entityType == FilterConstraintEntityType.Distributor)
                            ? entityId.ToString()
                            : null;
                    break;
                case "@monthnumber":
                    value = monthNumber.ToString();
                    break;
                default:
                    var parameterName = paramName.TrimStart('@');

                    if (parameterListDict.ContainsKey(parameterName))
                    {
                        value = string.Join(",", parameterListDict[parameterName]);
                    }

                    break;
            }

            if (value != null)
            {
                parameters.Add(new SqlParameter(paramName, value));
            }
        }

        return await masterDbSqlDataReader.GetSingleResultOfQuery(query, parameters);
    }
}
