﻿using Flurl.Http;
using Library.FaExceptions;
using PerfectStore.Core.Models;
using PerfectStore.Core.Models.DTOs;

namespace PerfectStore.Core.Utils.Helpers
{
    public interface ITargetAchievementService
    {
        Task<TargetAchievementModel> GetSingleTargetAch(
            long companyId,
            TargetAchievementDto targetAchievement,
            DateTime startDate,
            DateTime endDate
        );
    }

    public class TargetAchievementService(AppConfigSettings _appConfigSettings) : ITargetAchievementService
    {
        public async Task<TargetAchievementModel> GetSingleTargetAch(
            long companyId,
            TargetAchievementDto targetAchievement,
            DateTime startDate,
            DateTime endDate
        )
        {
            var api =
                $"{_appConfigSettings.targetApiBaseUrl}/api/v1/FlexibleTargets/GetSingleTargetAchievement?companyId={companyId}&targetId={targetAchievement.TargetMasterId}&companyTargetId={targetAchievement.CompanyTargetId}&hierarchy1Id={targetAchievement.Hierarchy1Id}&hierarchy2Id={targetAchievement.Hierarchy2Id}&hierarchy3Id={targetAchievement.Hierarchy3Id}&startDate={startDate.ToString("yyyy/MM/dd")}&endDate={endDate.ToString("yyyy/MM/dd")}";
            try
            {
                var resonse = await api.WithOAuthBearerToken(_appConfigSettings.targetApiToken)
                    .GetJsonAsync<TargetAchievementModel>();
                return resonse;
            }
            catch (FlurlHttpException ex)
            {
                // Handle HTTP errors
                var response = await ex.GetResponseStringAsync();
                throw new KnownIssueException(
                    $"Some error occurred at {api}, error: {response}",
                    ex
                );
            }
        }
    }
}
