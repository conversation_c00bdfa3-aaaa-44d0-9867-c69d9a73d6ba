﻿using PerfectStore.Core.Models.DbModels;

namespace PerfectStore.Core.Repositories.TransactionRepositories
{
    public interface IPerfectStoreOutletStatusRepository
    {
        Task UpsertPerfectStoreOutletStatus(
            List<PerfectStoreOutletStatus> newRecords,
            long companyId,
            long ruleId,
            DateTime startDate,
            DateTime endDate
        );
    }
}
