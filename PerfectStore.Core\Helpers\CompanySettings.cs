using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;

namespace PerfectStore.Core.Helpers
{
    public class CompanySettings
    {
        private readonly Dictionary<string, object> _settings;

        public CompanySettings(Dictionary<string, object> settings)
        {
            _settings = settings;
        }

        private string GetSettingByKey(string key)
        {
            if (_settings.TryGetValue(key, out var setting))
            {
                return setting.ToString();
            }

            return null;
        }

        private T GetSetting<T>(string key, T defaultValue)
        {
            try
            {
                var data = GetSettingByKey(key);
                if (!string.IsNullOrEmpty(data))
                {
                    return (T)
                        Convert.ChangeType(Convert.ChangeType(data, data.GetType()), typeof(T));
                }

                return defaultValue;
            }
            catch (Exception)
            {
                return defaultValue;
            }
        }

        private T GetSettingEnum<T>(string key, T defaultValue)
            where T : struct
        {
            try
            {
                var result = defaultValue;
                var value = GetSettingByKey(key).Replace(" ", "");
                if (!string.IsNullOrEmpty(value))
                {
                    Enum.TryParse(value, out result);
                }

                return result;
            }
            catch (Exception)
            {
                return defaultValue;
            }
        }

        private PositionCodeLevel GetSettingEnumForPositionCodeLevel(
            string key,
            PositionCodeLevel defaultValue
        )
        {
            try
            {
                var result = defaultValue;
                var value = PositionRoleExtension.PositionCodeLevelFromPositionLevel(
                    GetSettingByKey(key)
                );
                if (!string.IsNullOrEmpty(value))
                {
                    Enum.TryParse(value, out result);
                }

                return result;
            }
            catch (Exception)
            {
                return defaultValue;
            }
        }

        public bool IsCompanyUsesPerfectStore => GetSetting("CompanyUsesPerfectStore", false);

        public int YearStartMonth => GetSetting("yearStartMonth", 4);

        public TimeSpan TimeZoneOffset => GetSetting("TimeZoneOffset", TimeSpan.FromMinutes(330));

        public static CompanySettings Initialize(Dictionary<string, object> settings)
        {
            return new CompanySettings(settings);
        }
    }
}
