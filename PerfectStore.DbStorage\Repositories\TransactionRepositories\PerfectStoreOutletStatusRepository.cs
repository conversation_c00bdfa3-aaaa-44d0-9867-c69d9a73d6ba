﻿using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Repositories.TransactionRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.TransactionRepositories
{
    public class PerfectStoreOutletStatusRepository(
        WritableTransactionDbContext writableTransactionDbContext
    ) : IPerfectStoreOutletStatusRepository
    {
        public async Task UpsertPerfectStoreOutletStatus(
            List<PerfectStoreOutletStatus> newRecords,
            long companyId,
            long ruleId,
            DateTime startDate,
            DateTime endDate
        )
        {
            var existingRecords = await writableTransactionDbContext
                .PerfectStoreOutletStatus.Where(s =>
                    s.CompanyId == companyId
                    && s.PerfectStoreRuleId == ruleId
                    && s.StartDate >= startDate
                    && s.EndDate <= endDate
                )
                .ToListAsync();
            var newRecordToInsert = new List<PerfectStoreOutletStatus>();
            if (existingRecords?.Count > 0)
            {
                // update
                foreach (var newRecord in newRecords)
                {
                    var existingRecordDict = existingRecords
                        .GroupBy(s => s.OutletId)
                        .ToDictionary(gr => gr.Key, gr => gr.FirstOrDefault());

                    var key = newRecord.OutletId;
                    var existingRecord = existingRecordDict.GetValueOrDefault(key);
                    if (existingRecord != null)
                    {
                        existingRecord.IsCompleted = newRecord.IsCompleted;
                        existingRecord.CompletionRate = newRecord.CompletionRate;
                        existingRecord.CreationContext = newRecord.CreationContext;
                    }
                    else
                    {
                        newRecordToInsert.Add(newRecord);
                    }
                }
                if (newRecordToInsert.Count > 0)
                {
                    await writableTransactionDbContext.PerfectStoreOutletStatus.AddRangeAsync(
                        newRecordToInsert
                    );
                }
            }
            else
            {
                // insert
                await writableTransactionDbContext.PerfectStoreOutletStatus.AddRangeAsync(
                    newRecords
                );
            }

            await writableTransactionDbContext.SaveChangesAsync();
        }
    }
}
