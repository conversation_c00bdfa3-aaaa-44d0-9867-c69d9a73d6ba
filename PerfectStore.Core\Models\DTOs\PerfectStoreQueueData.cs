﻿namespace PerfectStore.Core.Models.DTOs
{
    public class PerfectStoreTaskQueueData
    {
        public long CompanyId { get; set; }
        public long RuleId { get; set; }
    }

    public class PerfectStoreTagQueueData
    {
        public long CompanyId { get; set; }
        public long RuleId { get; set; }
        public bool IsRepeatable { get; set; }
        public int RepeatFrequency { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EffectiveEndDate { get; set; }
        public DateTime CurrentDate { get; set; }
        public int YearStartMonth { get; set; }
        public TimeSpan TimeZoneOffset { get; set; }
        public List<long> FocusAreaIds { get; set; }
    }

    public class FocusAreaQueueData
    {
        public long CompanyId { get; set; }
        public long RuleId { get; set; }
        public bool IsRepeatable { get; set; }
        public int RepeatFrequency { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EffectiveEndDate { get; set; }
        public DateTime CurrentDate { get; set; }
    }

    public class PerfectStoreContinuousQueueData
    {
        public long CompanyId { get; set; }
    }
}
