﻿using System.ComponentModel.DataAnnotations.Schema;

namespace PerfectStore.Core.Models.DbModels;

public class StockistStock
{
    public long Id { get; set; }

    public long DistributorId { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public DateTime CreatedAt { get; set; }

    public long CompanyId { get; set; }

    public virtual ICollection<StockistStockItem> Items { get; set; }
}

public class StockistStockItem
{
    public long Id { get; set; }

    public long ProductId { get; set; }

    public int StockValue { get; set; }

    public long DistributorStockId { get; set; }

    [Column("ExpiryDate")]
    public DateTime? ExpiryDate { get; set; }

    [Column("Unit")]
    public string Unit { get; set; }

    [Column("Batch")]
    public string Batch { get; set; }

    public virtual StockistStock DistributorStock { get; set; }
}
