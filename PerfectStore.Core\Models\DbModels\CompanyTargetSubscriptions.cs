﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PerfectStore.Core.Models.DbModels
{
    public class CompanyTargetSubscriptions
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public long CompanyId { get; set; }

        public long TargetMasterId { get; set; }

        public bool IsPremium { get; set; }

        public bool IsDeactive { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public virtual TargetMaster TargetMaster { get; set; }

        public string TargetDescription { get; set; }
    }
}
