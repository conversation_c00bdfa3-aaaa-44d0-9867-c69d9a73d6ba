﻿using PerfectStore.Core.Models.DbModels;

namespace PerfectStore.Core.Repositories.UnifyClickhouseRepositories;

public interface IClickhouseKpiRepository
{
    Task<string?> GetClickhouseMetricValue(
    string query,
    long companyId,
    long entityId,
    FilterConstraintEntityType entityType,
    long? productDivisionId,
    DateTime startDate,
    DateTime endDate,
    int monthNumber,
    string? parameterValue = null);
}
