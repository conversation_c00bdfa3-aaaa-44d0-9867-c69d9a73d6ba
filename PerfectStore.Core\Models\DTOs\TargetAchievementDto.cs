﻿using Libraries.CommonEnums;

namespace PerfectStore.Core.Models.DTOs
{
    public class TargetAchievementDto
    {
        public long TargetId { get; set; }
        public long CompanyTargetId { get; set; }
        public long TargetMasterId { get; set; }
        public DateTime TargetStartPeriod { get; set; }
        public DateTime TargetEndPeriod { get; set; }
        public string AchievementQuery { get; set; }
        public OutletMetricQueryRelation AchievementDb { get; set; }
        public FlexibleTargetFrequency Frequency { get; set; }
        public double TargetValue { get; set; }
        public long Hierarchy1Id { get; set; }

        public long? Hierarchy2Id { get; set; }

        public long? Hierarchy3Id { get; set; }

        public FlexibleTargetEntityType Hierarchy1Type { get; set; }
        public FlexibleTargetEntityType? Hierarchy2Type { get; set; }
        public FlexibleTargetEntityType? Hierarchy3Type { get; set; }
    }
}
