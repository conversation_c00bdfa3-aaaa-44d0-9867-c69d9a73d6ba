﻿using Libraries.CommonEnums;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Models.DbModels.TransactionDbModels;

namespace PerfectStore.Core.Repositories.TransactionRepositories;

public interface IPerfectEntityTransactionalRepository
{
    Task<PerfectEntityCallDetail> GetPerfectEntityCallDetails(
        long companyId,
        long criteriaId,
        long ruleId,
        long dateKey,
        long entityId,
        FilterConstraintEntityType entityType
    );

    Task<List<PerfectEntityCallDetail>> GetPerfectEntityCallDetailsForDateRange(
        long companyId,
        long criteriaId,
        TaskManagementFocusAreaType criteriaType,
        long ruleId,
        long startDateKey,
        long endDateKey,
        long entityId,
        FilterConstraintEntityType entityType
    );
}
