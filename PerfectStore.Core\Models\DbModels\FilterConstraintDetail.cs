﻿using Libraries.CommonEnums;

namespace PerfectStore.Core.Models.DbModels;

public class FilterConstraintDetail
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public FilterConstraintEntityType EntityType { get; set; }
    public required string ConstraintJson { get; set; }

    public UserPlatform? UserPlatform { get; set; }
}

public class UserFilterConstraintJson
{
    public List<UserInfo> UserInfos { get; set; }
    public List<PositionInfo> PositionInfos { get; set; }
    public List<PositionCodeLevel> PositionCodeLevels { get; set; }
    public List<EmployeeRank> EmployeeRanks { get; set; }
    public List<EmployeeType> EmployeeTypes { get; set; }
}

public class UserInfo
{
    public List<long> Id { get; set; }
    public EmployeeRank Rank { get; set; }
}

public class PositionInfo
{
    public List<long> PositionId { get; set; }
    public PositionCodeLevel Level { get; set; }
}


public enum FilterConstraintEntityType
{
    User = 0,
    Outlet = 1,
    Distributor = 2,
}