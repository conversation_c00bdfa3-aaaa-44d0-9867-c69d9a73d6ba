﻿using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Models.DTOs;

namespace PerfectStore.Core.Repositories.MasterRepositories
{
    public interface ITaskManagementRepository
    {
        Task<List<TaskManagementFocusArea>> GetTaskManagementFocusAreasForProductTag(
            long companyId,
            long ruleId
        );

        Task<List<TaskManagementFocusArea>> GetTaskManagementFocusAreas(
            long companyId,
            List<long> ruleId
        );

        Task UpdateProductSuggestiveList(
            Dictionary<long, List<ProductSuggestedQtyDto>> qtyDict,
            long companyId,
            long focusAreaId
        );

        Task<List<TaskManagementTask>> GetTaskManagementTasks(
            long companyId,
            List<long> focusAreaIds
        );

        Task<List<FocusAreasDto>> GetTaskManagementFocusAreasWithTasks(long companyId, long ruleId);

        Task<TaskManagementFocusArea?> GetCompeleteTaskManagement(long companyId, long focusAreaId, long entityId);

        Task UpdateTaskAchievement(long companyId, Dictionary<long, (double,double)> taskAchievementDict);

        Task UpdateTaskSoldProductQuantites(long companyId, Dictionary<long, List<TaskManagementSoldProductQuantity>> updatedData);
    }
}
