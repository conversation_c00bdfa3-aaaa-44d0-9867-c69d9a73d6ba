{"profiles": {"DebugWritable": {"commandName": "Project", "environmentVariables": {"KEYVAULT_ENDPOINT": "https://v3DebugWritable.vault.azure.net/"}, "applicationUrl": "http://localhost:44362/"}, "ManageReadonly": {"commandName": "Project", "environmentVariables": {"BuildEnvironment": "Production", "KEYVAULT_ENDPOINT": "https://v3ManageReadonly.vault.azure.net/"}, "applicationUrl": "http://localhost:44362/"}, "ManageWritable": {"commandName": "Project", "environmentVariables": {"BuildEnvironment": "Production", "KEYVAULT_ENDPOINT": "https://v3ManageWritable.vault.azure.net/"}, "applicationUrl": "http://localhost:44362/"}, "Docker": {"commandName": "<PERSON>er"}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:57772/", "sslPort": 44362}}}