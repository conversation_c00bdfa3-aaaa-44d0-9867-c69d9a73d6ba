//using FluentAssertions;
//using Microsoft.Extensions.DependencyInjection;
//using NSubstitute;
//using PerfectEntityContinuousProcessor;
//using PerfectStore.Core.Helpers.QueueHandlers;
//using PerfectStore.Core.Models.DbModels;
//using PerfectStore.Core.Services;
//using System;
//using System.Text.Json;
//using System.Threading.Tasks;
//using Xunit;

//// Not a mock test

//namespace PerfectStore.Tests
//{
//    /// <summary>
//    /// Integration tests for the Perfect Entity Continuous Processor using xUnit
//    /// </summary>
//    public class PerfectEntityContinuousProcessorTests : IDisposable
//    {
//        private readonly ServiceProvider _serviceProvider;
//        private readonly IPerfectEntityContinuousService _continuousService;

//        /// <summary>
//        /// Constructor that runs before each test
//        /// </summary>
//        public PerfectEntityContinuousProcessorTests()
//        {
//            // Set up environment variables for testing
//            Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3DebugWritable.vault.azure.net/");

//            // Get configuration
//            var configuration = Configuration.GetConfiguration();

//            // Set up dependency injection
//            IServiceCollection serviceCollection = new ServiceCollection();
//            PerfectEntityContinuousProcessor.Configurations.Dependencies.SetUp(
//                configuration,
//                serviceCollection
//            );

//            // Build service provider
//            _serviceProvider = serviceCollection.BuildServiceProvider();

//            // Get the Perfect Entity Continuous Service
//            _continuousService = _serviceProvider.GetRequiredService<IPerfectEntityContinuousService>();
//        }

//        /// <summary>
//        /// Tests the continuous processor with a standard execution
//        /// </summary>
//        //[Fact(DisplayName = "Standard execution should succeed")]
//        //public async Task ProcessQueueAsync_StandardExecution_ShouldSucceed()
//        //{
//        //    // Arrange
//        //    var queueData = new PerfectEntityRuleQueueData
//        //    {
//        //        CompanyId = 10580,
//        //        RuleId = 1,
//        //        EntityId = 2827857,
//        //        FilterConstraintEntityType = FilterConstraintEntityType.Outlet,
//        //        Date = DateTime.Now
//        //    };

//        //    // Act
//        //    Func<Task> act = async () => await _continuousService.ProcessQueueAsync(queueData);

//        //    // Assert
//        //    await act.Should().NotThrowAsync("because standard processing should not throw an exception");
//        //}

//        /// <summary>
//        /// Tests the continuous processor with multiple consecutive executions
//        /// </summary>
//        //[Fact(DisplayName = "Multiple executions should succeed")]
//        //public async Task ProcessQueueAsync_MultipleExecutions_ShouldSucceed()
//        //{
//        //    // Arrange
//        //    var queueData = new PerfectEntityRuleQueueData
//        //    {
//        //        CompanyId = 10580,
//        //        RuleId = 1,
//        //        EntityId = 2827857,
//        //        FilterConstraintEntityType = FilterConstraintEntityType.Outlet,
//        //        Date = DateTime.Now
//        //    };

//        //    // Act & Assert - First execution
//        //    await _continuousService.ProcessQueueAsync(queueData);

//        //    // Act & Assert - Second execution
//        //    Func<Task> secondExecution = async () => await _continuousService.ProcessQueueAsync(queueData);
//        //    await secondExecution.Should().NotThrowAsync("because multiple executions should not throw exceptions");
//        //}

//        /// <summary>
//        /// Tests the continuous processor with a mock service
//        /// </summary>
//        // [Fact(DisplayName = "Execution with mock service should succeed")]
//        //public async Task ProcessQueueAsync_WithMockService_ShouldSucceed()
//        //{
//        //    // Arrange
//        //    var mockService = Substitute.For<IPerfectEntityContinuousService>();
//        //    var queueData = new PerfectEntityRuleQueueData
//        //    {
//        //        CompanyId = 10580,
//        //        RuleId = 1,
//        //        EntityId = 2827857,
//        //        FilterConstraintEntityType = FilterConstraintEntityType.Outlet,
//        //        Date = DateTime.Now
//        //    };

//        //    // Act
//        //    Func<Task> act = async () => await mockService.ProcessQueueAsync(queueData);

//        //    // Assert
//        //    await act.Should().NotThrowAsync("because processing with a mock service should not throw an exception");

//        //    // Verify the mock was called
//        //    await mockService.Received().ProcessQueueAsync(Arg.Is<PerfectEntityRuleQueueData>(d =>
//        //        d.CompanyId == queueData.CompanyId &&
//        //        d.RuleId == queueData.RuleId &&
//        //        d.EntityId == queueData.EntityId));
//        //}

//        [Fact]
//        public async Task Test()
//        {
//            try
//            {
//                var json = "{\"RuleId\":143,\"RuleType\":0,\"CompanyId\":10580,\"CriteriaId\":169,\"Date\":\"2025-06-02T00:00:00\",\"EntityType\":1,\"EntityIdBlobPath\":\"10580/143/169/entityIds_1.json\",\"ContainerName\":\"perfect-entity\",\"ProductDivisionIds\":\"\",\"QualifierIds\":\"\",\"QualifierRelation\":\"\"}";
//                var data = JsonSerializer.Deserialize<PerfectEntityRuleQueueData>(json);

//                //var data = new PerfectEntityRuleQueueData
//                //{
//                //    RuleId = 1,
//                //    CompanyId = 182954,
//                //    Date = new DateTime(2025, 04, 01),
//                //};
//                var processor = _serviceProvider.GetRequiredService<IPerfectEntityContinuousService>();
//                await processor.ProcessQueueAsync(data);
//            }
//            catch (Exception ex)
//            {
//                Console.WriteLine(ex.Message);
//                throw;
//            }
//        }

//        /// <summary>
//        /// Cleanup method that runs after each test
//        /// </summary>
//        public void Dispose()
//        {
//            // Dispose of the service provider
//            _serviceProvider?.Dispose();
//            GC.SuppressFinalize(this);
//        }
//    }
//}
