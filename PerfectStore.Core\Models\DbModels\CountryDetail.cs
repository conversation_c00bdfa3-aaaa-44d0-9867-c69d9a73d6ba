﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Library.NumberSystem;


namespace PerfectStore.Core.Models.DbModels
{
    public class CountryDetail
    {
        [Key] public string CountryName { get; set; }


        [NotMapped]
        public TimeSpan TimeZoneOffset
        {
            get => TimeSpan.FromMinutes(TimeZoneOffsetMinutes);
            set => TimeZoneOffsetMinutes = (int)value.TotalMinutes;
        }

        public int TimeZoneOffsetMinutes { get; set; }

        public string TimeZoneOffsetMinutesStr
        {
            get
            {
                var hour = TimeZoneOffsetMinutes / 60;
                var hourStr = (hour > 0 ? "+" : "") + $"{hour:D2}";
                var minute = TimeZoneOffsetMinutes < 0 ? -TimeZoneOffsetMinutes % 60 : TimeZoneOffsetMinutes % 60;
                var minuteStr = $"{minute:D2}";
                return $"GMT{hourStr}:{minuteStr}";
            }
        }

        public string CurrencyName { get; set; }
        public string CurrencySymbol { get; set; }
        public string Language { get; set; }
        public string PhNoPrefix { get; set; }
        public int DigitsInPhNo { get; set; }
        public NumberSystems NumberSystem { get; set; }
    }
}
