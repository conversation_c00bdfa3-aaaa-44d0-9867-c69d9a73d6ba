using System.Data;
using System.Text.Json;
using Libraries.CommonEnums;
using Library.CommonHelpers;
using Library.DateTimeHelpers;
using Library.StorageWriter.Reader_Writer;
using PerfectStore.Core.Helpers;
using PerfectStore.Core.Helpers.QueueHandlers;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Models.DbModels.ClickhouseDbModels;
using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.Core.Repositories.ReportRepositories;
using PerfectStore.Core.Repositories.TransactionRepositories;
using PerfectStore.Core.Repositories.UnifyClickhouseRepositories;
using PerfectStore.Core.Utils.Helpers;

namespace PerfectStore.Core.Services;

public interface IPerfectEntityContinuousService
{
    Task ProcessQueueAsync(PerfectEntityRuleQueueData data);
}

public class PerfectEntityContinuousService(
    IPerfectEntityRepository perfectEntityRepository,
    ICompanyTargetRepository companyTargetRepository,
    ITargetAchievementService targetAchievementService,
    IPerfectEntityRuleAnalyticClickhouseRepository perfectEntityRuleAnalyticClickhouseRepository,
    IPerfectEntityTransactionalRepository perfectEntityTransactionalRepository,
    KpiService kpiService,
    FaiDataLakeBlobWriter faiDataLakeBlobWriter,
    ITaskManagementRepository taskManagementRepository,
    IProductRepository productRepository,
    IAttendanceRepository attendanceRepository,
    IDmsInvoiceRepository dmsInvoiceRepository,
    INonFAInvoiceRepository nonFAInvoiceRepository,
    IDistributorRepository distributorRepository,
    IOutletMetricRepository outletMetricRepository,
    IKpiRepository kpiRepository
) : IPerfectEntityContinuousService
{
    public async Task ProcessQueueAsync(PerfectEntityRuleQueueData data)
    {
        var todayDate = data.TodayDate;
        var startDate = data.StartDate;
        var endDate = data.EndDate;
        var monthNumber = data.MonthNumber;

        var criteria = await perfectEntityRepository.GetPerfectEntityCriteriaByCriteriaId(
            data.CompanyId,
            data.CriteriaId
        );

        var dataToSave = new List<PerfectEntityRuleAnalytic>();

        switch (data.RuleType)
        {
            case PerfectEntityRuleType.Call:
            case PerfectEntityRuleType.Store:
                var res = await ProcessStoreBasedRule(data.RuleId, data.CompanyId, data.EntityId, data.ProductDivisionId,
                    data.EntityType, criteria, todayDate, startDate, endDate);
                if (res != null)
                {
                    dataToSave.AddRange(res);
                }
                break;
            case PerfectEntityRuleType.Entity:
                var incentiveData = await ProcessIncentiveBasedRule(
                    data.RuleId,
                    data.CompanyId,
                    data.EntityId,
                    data.ProductDivisionId,
                    data.EntityType,
                    criteria,
                    todayDate,
                    startDate,
                    endDate,
                    monthNumber,
                    data.QualifierIds,
                    data.QualifierRelation
                );
                if (incentiveData != null)
                {
                    dataToSave.AddRange(incentiveData);
                }
                break;
        }

        Console.WriteLine($"Count of data getting updated {dataToSave.Count}");

        if (dataToSave.Count > 0)
        {
            await perfectEntityRuleAnalyticClickhouseRepository.SavePerfectEntityRuleAnalyticsAsync(
                dataToSave
            );

            Console.WriteLine($"Data Saved in clickhouse successfully {dataToSave.Count}");
        }
    }

    private async Task<List<PerfectEntityRuleAnalytic>?> ProcessIncentiveBasedRule(
        long ruleId,
        long companyId,
        List<long> entityIds,
        List<long>? pdIds,
        FilterConstraintEntityType entityType,
        PerfectEntityRuleCriteria? criteria,
        DateTime todayDate,
        DateTime startDate,
        DateTime endDate,
        int monthNumber,
        string? ruleQualifierIdsString,
        string? ruleQualifierRelation)
    {
        var dataToSave = new List<PerfectEntityRuleAnalytic>();

        // Move qualifier ID parsing outside the loops - parse once instead of for each entity/pd combination
        var ruleQualifierIds = !string.IsNullOrEmpty(ruleQualifierIdsString) ?
            JsonSerializer.Deserialize<List<long>>(ruleQualifierIdsString) : null;

        var criteriaQualifierIds = !string.IsNullOrEmpty(criteria?.QualifierIds) ?
            JsonSerializer.Deserialize<List<long>>(criteria?.QualifierIds ?? string.Empty) : null;

        var combinedQualifierId = ruleQualifierIds?.Union(criteriaQualifierIds ?? []).Distinct().ToList();

        // Pre-fetch criteria entity details if needed - avoid repeated database calls
        PerfectEntityQualifier? criteriaEntityDetails = null;
        GlobalOutletMetrices? globalOutletMeterics = null;
        Kpi? kpi = null;
        if (criteria?.CriteriaType == TaskManagementFocusAreaType.Qualifier && criteria.CriteriaEntityId.HasValue)
        {
            criteriaEntityDetails = (await perfectEntityRepository.GetPerfectEntityQualifiers(companyId, [criteria.CriteriaEntityId.Value]))
                .FirstOrDefault();

            globalOutletMeterics = criteriaEntityDetails.KPIType == QualifierKpiType.GlobalOutletMetrices 
                 ? await outletMetricRepository.GetOutletMetrics(criteriaEntityDetails.KPIId) : null;

           kpi = criteriaEntityDetails.KPIType == QualifierKpiType.GlobalKpis
                ? await kpiRepository.GetKpiById(criteriaEntityDetails.KPIId, companyId)
                : null;
        }

        var qualifiers = await perfectEntityRepository.GetPerfectEntityQualifiers(companyId, combinedQualifierId);

        foreach (var entityId in entityIds)
        {
            if (pdIds?.Count > 0)
            {
                foreach (var pdId in pdIds ?? [])
                {
                    // Get qualifier evaluation results for this specific entity/pd combination
                    var qualifiersDict = await GetQualifierEvaluationResults(companyId, entityId, pdId,
                        entityType, combinedQualifierId, todayDate, startDate, endDate, monthNumber, qualifiers);

                    // Filter qualifiers based on pre-parsed IDs - fixed logic from original
                    var ruleQualifiersDict = ruleQualifierIds != null ?
                        qualifiersDict.Where(s => ruleQualifierIds.Contains(s.Key)).ToDictionary(s => s.Key, s => s.Value)
                        : null;

                    var criteriaQualifiersDict = criteriaQualifierIds != null ?
                        qualifiersDict.Where(s => criteriaQualifierIds.Contains(s.Key)).ToDictionary(s => s.Key, s => s.Value)
                        : null;

                    // Evaluate qualifier relations
                    var isRuleQualifierFulfilled = ruleQualifiersDict != null
                        ? EvaluateQualifierRelations(ruleQualifierRelation, ruleQualifiersDict)
                        : (bool?)null;

                    var isCriteriaQualifierFulfilled = criteriaQualifiersDict != null
                        ? EvaluateQualifierRelations(criteria?.QualifierRelation, criteriaQualifiersDict)
                        : (bool?)null;

                    // Process based on qualifier evaluation results
                    PerfectEntityRuleAnalytic analyticData;

                    // Check if qualifiers are met
                    if ((isRuleQualifierFulfilled == true && isCriteriaQualifierFulfilled == true)
                        || (isRuleQualifierFulfilled == null && isCriteriaQualifierFulfilled == null))
                    {
                        if (criteria?.CriteriaType == TaskManagementFocusAreaType.Qualifier && criteriaEntityDetails != null)
                        {
                            var res = await EvaluateKpiQualifier(companyId, entityId, pdId, entityType,
                                criteriaEntityDetails, todayDate, startDate, endDate, monthNumber, globalOutletMeterics, kpi);

                            analyticData = CreateAnalyticData(companyId, entityId, pdId, ruleId, entityType,
                                criteria, todayDate, res.AchPercentage, criteriaEntityDetails.Target, res.AchValue?.ToString());
                        }
                        else
                        {
                            // Handle case where criteria type is not Qualifier or criteriaEntityDetails is null
                            analyticData = CreateAnalyticData(companyId, entityId, pdId, ruleId, entityType,
                                criteria, todayDate, 0, null, null);
                        }
                    }
                    else
                    {
                        // Qualifiers not met
                        analyticData = CreateAnalyticData(companyId, entityId, pdId, ruleId, entityType,
                            criteria, todayDate, 0, null, null);
                    }

                    // Assign rewards
                    var rewardId = await AssignRewards(companyId, criteria, analyticData);
                    analyticData.RewardId = rewardId ?? 0;

                    dataToSave.Add(analyticData);
                }
            }
            else
            {
                long? pdId = null;
                // Get qualifier evaluation results for this specific entity/pd combination
                var qualifiersDict = await GetQualifierEvaluationResults(companyId, entityId, pdId,
                    entityType, combinedQualifierId, todayDate, startDate, endDate, monthNumber, qualifiers);

                // Filter qualifiers based on pre-parsed IDs - fixed logic from original
                var ruleQualifiersDict = ruleQualifierIds != null ?
                    qualifiersDict.Where(s => ruleQualifierIds.Contains(s.Key)).ToDictionary(s => s.Key, s => s.Value)
                    : null;

                var criteriaQualifiersDict = criteriaQualifierIds != null ?
                    qualifiersDict.Where(s => criteriaQualifierIds.Contains(s.Key)).ToDictionary(s => s.Key, s => s.Value)
                    : null;

                // Evaluate qualifier relations
                var isRuleQualifierFulfilled = ruleQualifiersDict != null
                    ? EvaluateQualifierRelations(ruleQualifierRelation, ruleQualifiersDict)
                    : (bool?)null;

                var isCriteriaQualifierFulfilled = criteriaQualifiersDict != null
                    ? EvaluateQualifierRelations(criteria?.QualifierRelation, criteriaQualifiersDict)
                    : (bool?)null;

                // Process based on qualifier evaluation results
                PerfectEntityRuleAnalytic analyticData;

                // Check if qualifiers are met
                if ((isRuleQualifierFulfilled == true && isCriteriaQualifierFulfilled == true)
                    || (isRuleQualifierFulfilled == null && isCriteriaQualifierFulfilled == null))
                {
                    if (criteria?.CriteriaType == TaskManagementFocusAreaType.Qualifier && criteriaEntityDetails != null)
                    {
                        var res = await EvaluateKpiQualifier(companyId, entityId, pdId, entityType,
                            criteriaEntityDetails, todayDate, startDate, endDate, monthNumber, globalOutletMeterics, kpi);

                        analyticData = CreateAnalyticData(companyId, entityId, pdId, ruleId, entityType,
                            criteria, todayDate, res.AchPercentage, criteriaEntityDetails.Target, res.AchValue?.ToString());
                    }
                    else
                    {
                        // Handle case where criteria type is not Qualifier or criteriaEntityDetails is null
                        analyticData = CreateAnalyticData(companyId, entityId, pdId, ruleId, entityType,
                            criteria, todayDate, 0, null, null);
                    }
                }
                else
                {
                    // Qualifiers not met
                    analyticData = CreateAnalyticData(companyId, entityId, pdId, ruleId, entityType,
                        criteria, todayDate, 0, null, null);
                }

                // Assign rewards
                var rewardId = await AssignRewards(companyId, criteria, analyticData);
                analyticData.RewardId = rewardId ?? 0;

                dataToSave.Add(analyticData);
            }
        }

        return dataToSave;
    }


    private async Task<Dictionary<long, bool>> GetQualifierEvaluationResults(long companyId,
        long entityId,
        long? pdId,
        FilterConstraintEntityType entityType,
        List<long>? qualifierIds,
        DateTime todayDate,
        DateTime startDate,
        DateTime endDate,
        int monthNumber,
        List<PerfectEntityQualifier> qualifiers)
    {
        var result = new Dictionary<long, bool>();

        if (qualifierIds?.Count > 0)
        {
            foreach (var qualifier in qualifiers)
            {
                var res = await EvaluateKpiQualifier(companyId, entityId, pdId, entityType, qualifier, todayDate, startDate, endDate, monthNumber);
                result[qualifier.Id] = res.IsPassed;
            }
        }

        return result;
    }

    private async Task<KpiCheckModel> EvaluateKpiQualifier(
        long companyId,
        long entityId,
        long? pdId,
        FilterConstraintEntityType entityType,
        PerfectEntityQualifier qualifier,
        DateTime todayDate,
        DateTime startDate,
        DateTime endDate,
        int monthNumber,
        GlobalOutletMetrices? globalOutletMetrices = null,
        Kpi? kpi = null)
    {
        return qualifier.KPIType switch
        {
            QualifierKpiType.GlobalKpis => await kpiService.ProcessGlobalKpisQualifier(companyId, entityId, pdId, entityType, qualifier, todayDate, startDate, endDate, monthNumber, kpi),
            QualifierKpiType.GlobalOutletMetrices => await kpiService.ProcessGlobalOutletMetrices(companyId, entityId, pdId, entityType , qualifier, todayDate, startDate, endDate, monthNumber, globalOutletMetrices),
            _ => throw new NotImplementedException(),
        };
    }

    private static bool EvaluateQualifierRelations(string? qualifierRelationJson, Dictionary<long, bool> qualifierResults)
    {
        if (string.IsNullOrEmpty(qualifierRelationJson))
        {
            return false;
        }

        var criteriaQualifierRelation = JsonSerializer.Deserialize<QualifierRelationModel>(qualifierRelationJson);
        if (criteriaQualifierRelation == null)
        {
            return false;
        }

        return EvaluateRelation(criteriaQualifierRelation, qualifierResults);
    }

    private static bool EvaluateRelation(QualifierRelationModel relation, Dictionary<long, bool> qualifierResults)
    {
        var evaluationResults = new List<bool>();

        // Evaluate nested relations recursively
        if (relation.QualifiersRelations != null && relation.QualifiersRelations.Any())
        {
            foreach (var subRelation in relation.QualifiersRelations)
            {
                evaluationResults.Add(EvaluateRelation(subRelation, qualifierResults));
            }
        }

        // Evaluate direct QualifierIds
        if (relation.QualifierIds != null && relation.QualifierIds.Any())
        {
            var qualifierResultsList = relation.QualifierIds
                .Select(id => qualifierResults.TryGetValue(id, out bool result) && result)
                .ToList();

            bool qualifierEvaluation = relation.Operation == "AND"
                ? qualifierResultsList.All(r => r)
                : qualifierResultsList.Any(r => r);

            evaluationResults.Add(qualifierEvaluation);
        }

        // Final evaluation based on operation
        return relation.Operation == "AND"
            ? evaluationResults.All(r => r)
            : evaluationResults.Any(r => r);
    }

    private async Task<List<PerfectEntityRuleAnalytic>?> ProcessStoreBasedRule(
        long ruleId,
        long companyId,
        long entityId,
        long? pdId,
        FilterConstraintEntityType entityType,
        PerfectEntityRuleCriteria? perfectEntityRuleCriteria,
        DateTime todayDate,
        DateTime startDate,
        DateTime endDate)
    {

        if (perfectEntityRuleCriteria == null)
        {
            return null;
        }

        var dataToSave = new List<PerfectEntityRuleAnalytic>();

        await ProcessCriteria(companyId, entityId, pdId, ruleId, entityType, perfectEntityRuleCriteria, todayDate, startDate, endDate, dataToSave);

        return dataToSave;
    }

    private async Task ProcessCriteria(
        long companyId,
        long entityId,
        long? pdId,
        long ruleId,
        FilterConstraintEntityType entityType,
        PerfectEntityRuleCriteria criteria,
        DateTime todayDate,
        DateTime startDate,
        DateTime endDate,
        List<PerfectEntityRuleAnalytic> dataToSave)
    {
        switch (criteria.CriteriaType)
        {
            case TaskManagementFocusAreaType.TargetAchievementBased:
                await ProcessTargetVsAchievementTypeCriteria(companyId, entityId, pdId, ruleId, entityType, criteria, todayDate, startDate, endDate, dataToSave);
                break;
            case TaskManagementFocusAreaType.AssetManagementBased:
            case TaskManagementFocusAreaType.SurveyBased:
            case TaskManagementFocusAreaType.PerfectCallBased:
            case TaskManagementFocusAreaType.ProductRecommendationBased:
                await ProcessGeneralCriteria(companyId, entityId, ruleId, entityType, pdId, criteria, todayDate, dataToSave);
                break;
            case TaskManagementFocusAreaType.FocusArea:
                await ProcessFocusAreaTypeCriteria(companyId, entityId, pdId, ruleId, entityType, criteria, todayDate, startDate, endDate, dataToSave);
                break;
        }
    }

    private async Task ProcessFocusAreaTypeCriteria(
        long companyId,
        long entityId,
        long? pdId,
        long ruleId,
        FilterConstraintEntityType entityType,
        PerfectEntityRuleCriteria? criteria,
        DateTime todayDate,
        DateTime startDate,
        DateTime endDate,
        List<PerfectEntityRuleAnalytic> dataToSave)
    {
        if (criteria == null)
        {
            return;
        }

        var focusAreaId = criteria.CriteriaEntityId;

        if (focusAreaId == null)
        {
            return;
        }

        var taskManagementFocusArea = await taskManagementRepository.GetCompeleteTaskManagement(companyId, focusAreaId.Value, entityId);
        if (taskManagementFocusArea == null)
        {
            return;
        }

        var taskManagementTasks = taskManagementFocusArea.TaskManagementTasks;
        if (taskManagementTasks == null || taskManagementTasks.Count == 0)
        {
            return;
        }

        var filteredTask = taskManagementTasks.Where(s => s.TaskEntityId == entityId).FirstOrDefault();

        var outletSales = await GetSales(companyId, entityId, startDate.GetDateKey(), endDate.GetDateKey(), taskManagementFocusArea.SaleType ?? SalesType.Order);

        var taskAchievementDict = new Dictionary<long, (double, double)>();
        var taskSoldProductQuantitesDict = new Dictionary<long, List<TaskManagementSoldProductQuantity>>();

        var doCalculationToShowTagInApp = true;

        var companyProducts = await productRepository.GetProductWithMOQAndCategory(companyId);

        switch (taskManagementFocusArea.Type)
        {
            case TaskManagementFocusAreaType.NumberBased:
                
                foreach (var task in filteredTasks)
                {
                    var taskSales = outletSales.TryGetValue(task.TaskEntityId ?? 0, out var sale) ? sale : [];

                    if (taskSales == null || taskSales.Count == 0)
                    {
                        taskAchievementDict.Add(task.Id, ((double, double))(task.TaskTarget ?? 0, 0));
                    }
                    else
                    {
                        // Apply filtering based on task level hierarchy if specified
                        var filteredSales = taskSales;

                        if (task.TaskLevelHierarchyIdsList != null && task.TaskLevelHierarchyIdsList.Count != 0)
                        {
                            switch (taskManagementFocusArea.TaskLevelHierarchy)
                            {
                                case ProductLevelHierarchy.DisplayCategory:
                                    var displayCatProductIds = companyProducts
                                        .Where(p => task.TaskLevelHierarchyIdsList.Contains(p.ProductDisplayCategoryId ?? 0))
                                        .Select(p => p.Id)
                                        .ToList();

                                    filteredSales = taskSales.Where(s => displayCatProductIds.Contains(s.ProductId)).ToList();
                                    break;

                                case ProductLevelHierarchy.ProductGroup:
                                    var productGroupProductIds = companyProducts
                                        .Where(p => task.TaskLevelHierarchyIdsList.Contains(p.ProductGroupId ?? 0))
                                        .Select(p => p.Id)
                                        .ToList();

                                    filteredSales = taskSales.Where(s => productGroupProductIds.Contains(s.ProductId)).ToList();
                                    break;
                            }
                        }

                        var achievement = filteredSales?.Sum(s => s.RevenueSales) ?? 0;
                        taskAchievementDict.Add(task.Id, ((double, double))(task.TaskTarget ?? 0, achievement));
                    }
                }

                break;
            case TaskManagementFocusAreaType.ProductTag:
                switch (taskManagementFocusArea.TaskLevelHierarchy)
                {
                    case ProductLevelHierarchy.DisplayCategory:
                        foreach (var task in filteredTasks)
                        {
                            // if task level ids are null then we are talking about all the display cat in the company
                            var displayCatIds = task.TaskLevelHierarchyIdsList ?? companyProducts
                                                          .Where(s => s.ProductDisplayCategoryId.HasValue)
                                                          .Select(s => s.ProductDisplayCategoryId!.Value)
                                                          .ToList();

                            var productIds = companyProducts.Where(s => displayCatIds.Contains(s.ProductDisplayCategoryId ?? 0)).Select(s => s.Id).ToList();
                            var outletSaleList = outletSales.ContainsKey(task.TaskEntityId ?? 0) ? outletSales[task.TaskEntityId ?? 0] : [];
                            if (outletSaleList.Count == 0)
                            {
                                if (task.TaskLevelHierarchyIdsList == null)
                                {
                                    continue;
                                }

                                taskAchievementDict.Add(task.Id, ((double, double))(task.TaskTarget, 0));

                                var upsertSoldQtyTasks = productIds
                                    .Select(productId =>
                                    {
                                        var moq = companyProducts.FirstOrDefault(p => p.Id == productId)?.MOQ ?? 0;
                                        var displayCategoryId = companyProducts.FirstOrDefault(p => p.Id == productId)?.ProductDisplayCategoryId ?? 0;

                                        return new TaskManagementSoldProductQuantity
                                        {
                                            TaskManagementTaskId = task.Id,
                                            ProductLevelHieararchyId = productId,
                                            TaskLevelHieararchyId = displayCategoryId,
                                            BilledQty = 0,
                                            RemainingBilledQty = Math.Max(moq, 0),
                                            ShowTagInApp = false,
                                            CompanyId = companyId,
                                            CreatedAt = DateTime.UtcNow,
                                            LastUpdatedAt = DateTime.UtcNow,
                                            MOQ = moq
                                        };
                                    })
                                    .ToList();

                                taskSoldProductQuantitesDict.Add(task.Id, upsertSoldQtyTasks);
                            }
                            else if (task.TaskLevelHierarchyIdsList == null)
                            {
                                doCalculationToShowTagInApp = false;
                                // Handle: task-level hierarchy is NULL → one product per display category that meets MOQ
                                var productSales = outletSaleList
                                    .Join(companyProducts, sale => sale.ProductId, prod => prod.Id, (sale, prod) => new
                                    {
                                        prod.ProductDisplayCategoryId,
                                        ProductId = sale.ProductId,
                                        sale.QuantitySales,
                                        MOQ = prod.MOQ ?? 0
                                    })
                                    .Where(p => p.ProductDisplayCategoryId.HasValue)
                                    .ToList();

                                var groupedByDisplayCat = productSales
                                    .GroupBy(p => p.ProductDisplayCategoryId!.Value)
                                    .ToList();

                                int achievement = 0;
                                var upsertSoldQtyTasks = new List<TaskManagementSoldProductQuantity>();

                                foreach (var group in groupedByDisplayCat)
                                {
                                    var firstMatching = group.FirstOrDefault(p => p.QuantitySales >= p.MOQ);
                                    if (firstMatching != null)
                                    {
                                        achievement++;

                                        upsertSoldQtyTasks.Add(new TaskManagementSoldProductQuantity
                                        {
                                            TaskManagementTaskId = task.Id,
                                            ProductLevelHieararchyId = firstMatching.ProductId,
                                            TaskLevelHieararchyId = group.Key,
                                            BilledQty = firstMatching.QuantitySales,
                                            RemainingBilledQty = Math.Max(firstMatching.MOQ - firstMatching.QuantitySales, 0),
                                            ShowTagInApp = false,
                                            CompanyId = companyId,
                                            CreatedAt = DateTime.UtcNow,
                                            LastUpdatedAt = DateTime.UtcNow,
                                            MOQ = firstMatching.MOQ
                                        });
                                    }
                                }

                                taskAchievementDict.Add(task.Id, ((double, double))(task.TaskTarget, achievement));
                                taskSoldProductQuantitesDict.Add(task.Id, upsertSoldQtyTasks);
                            }
                            else
                            {
                                var productSales = outletSaleList.Where(s => productIds.Contains(s.ProductId))
                                    .Select(s => new
                                    {
                                        DisplayCategoryId = companyProducts.FirstOrDefault(p => p.Id == s.ProductId)?.ProductDisplayCategoryId,
                                        s.ProductId,
                                        s.RevenueSales,
                                        s.QuantitySales,
                                        s.StdQuantitySales,
                                        s.OutletId
                                    })
                                    .Where(s => s.DisplayCategoryId != null)
                                    .ToList();

                                var dispCatWiseSales = productSales.GroupBy(s => s.DisplayCategoryId).ToDictionary(gr => gr.Key, gr => gr.ToList());

                                var achievement = 0;
                                foreach (var sale in dispCatWiseSales)
                                {
                                    var displayCatId = sale.Key;
                                    var productSalesList = sale.Value;
                                    var completedMoqCount = productSalesList.Count(s => s.QuantitySales >= companyProducts.FirstOrDefault(p => p.Id == s.ProductId)?.MOQ);
                                    if (completedMoqCount >= 1)
                                    {
                                        achievement += 1;
                                    }
                                }

                                taskAchievementDict.Add(task.Id, ((double, double))(task.TaskTarget, achievement));

                                var upsertSoldQtyTasks = productIds.Select(productId =>
                                {
                                    var qtySale = productSales.Where(r => r.ProductId == productId).Sum(r => r.QuantitySales);
                                    var moq = companyProducts.FirstOrDefault(p => p.Id == productId)?.MOQ ?? 0;
                                    var displayCategoryId = companyProducts.FirstOrDefault(p => p.Id == productId)?.ProductDisplayCategoryId ?? 0;

                                    return new TaskManagementSoldProductQuantity
                                    {
                                        TaskManagementTaskId = task.Id,
                                        ProductLevelHieararchyId = productId,
                                        TaskLevelHieararchyId = displayCategoryId,
                                        BilledQty = qtySale,
                                        RemainingBilledQty = Math.Max(moq - qtySale, 0),
                                        ShowTagInApp = false,
                                        CompanyId = companyId,
                                        CreatedAt = DateTime.UtcNow,
                                        LastUpdatedAt = DateTime.UtcNow,
                                        MOQ = moq
                                    };
                                }).ToList();

                                taskSoldProductQuantitesDict.Add(task.Id, upsertSoldQtyTasks);
                            }
                        }
                        break;
                    case ProductLevelHierarchy.ProductGroup:
                        foreach (var task in filteredTasks)
                        {
                            var displayCatIds = task.TaskLevelHierarchyIdsList ?? companyProducts
                                                          .Where(s => s.ProductGroupId.HasValue)
                                                          .Select(s => s.ProductGroupId!.Value)
                                                          .ToList();

                            var productIds = companyProducts.Where(s => displayCatIds.Contains(s.ProductGroupId ?? 0)).Select(s => s.Id).ToList();
                            var outletSaleList = outletSales.ContainsKey(task.TaskEntityId ?? 0) ? outletSales[task.TaskEntityId ?? 0] : [];

                            if (outletSaleList.Count == 0)
                            {
                                taskAchievementDict.Add(task.Id, ((double, double))(task.TaskTarget, 0));

                                var upsertSoldQtyTasks = productIds
                                    .Select(productId =>
                                    {
                                        var moq = companyProducts.FirstOrDefault(p => p.Id == productId)?.MOQ ?? 0;
                                        var productGroupId = companyProducts.FirstOrDefault(p => p.Id == productId)?.ProductGroupId ?? 0;

                                        return new TaskManagementSoldProductQuantity
                                        {
                                            TaskManagementTaskId = task.Id,
                                            ProductLevelHieararchyId = productId,
                                            TaskLevelHieararchyId = productGroupId,
                                            BilledQty = 0,
                                            RemainingBilledQty = Math.Max(moq, 0),
                                            ShowTagInApp = false,
                                            CompanyId = companyId,
                                            CreatedAt = DateTime.UtcNow,
                                            LastUpdatedAt = DateTime.UtcNow,
                                            MOQ = moq
                                        };
                                    })
                                    .ToList();

                                taskSoldProductQuantitesDict.Add(task.Id, upsertSoldQtyTasks);
                            }
                            else if (task.TaskLevelHierarchyIdsList == null)
                            {
                                doCalculationToShowTagInApp = false;
                                // Handle: task-level hierarchy is NULL → one product per product group id that meets MOQ
                                var productSales = outletSaleList
                                    .Join(companyProducts, sale => sale.ProductId, prod => prod.Id, (sale, prod) => new
                                    {
                                        prod.ProductGroupId,
                                        ProductId = sale.ProductId,
                                        sale.QuantitySales,
                                        MOQ = prod.MOQ ?? 0
                                    })
                                    .Where(p => p.ProductGroupId.HasValue)
                                    .ToList();

                                var groupedByDisplayCat = productSales
                                    .GroupBy(p => p.ProductGroupId!.Value)
                                    .ToList();

                                int achievement = 0;
                                var upsertSoldQtyTasks = new List<TaskManagementSoldProductQuantity>();

                                foreach (var group in groupedByDisplayCat)
                                {
                                    var firstMatching = group.FirstOrDefault(p => p.QuantitySales >= p.MOQ);
                                    if (firstMatching != null)
                                    {
                                        achievement++;

                                        upsertSoldQtyTasks.Add(new TaskManagementSoldProductQuantity
                                        {
                                            TaskManagementTaskId = task.Id,
                                            ProductLevelHieararchyId = firstMatching.ProductId,
                                            TaskLevelHieararchyId = group.Key,
                                            BilledQty = firstMatching.QuantitySales,
                                            RemainingBilledQty = Math.Max(firstMatching.MOQ - firstMatching.QuantitySales, 0),
                                            ShowTagInApp = false,
                                            CompanyId = companyId,
                                            CreatedAt = DateTime.UtcNow,
                                            LastUpdatedAt = DateTime.UtcNow,
                                            MOQ = firstMatching.MOQ
                                        });
                                    }
                                }

                                taskAchievementDict.Add(task.Id, ((double, double))(task.TaskTarget, achievement));
                                taskSoldProductQuantitesDict.Add(task.Id, upsertSoldQtyTasks);
                            }
                            else
                            {
                                var productSales = outletSaleList.Where(s => productIds.Contains(s.ProductId))
                                    .Select(s => new
                                    {
                                        ProductGroupId = companyProducts.FirstOrDefault(p => p.Id == s.ProductId)?.ProductGroupId,
                                        s.ProductId,
                                        s.RevenueSales,
                                        s.QuantitySales,
                                        s.StdQuantitySales,
                                        s.OutletId
                                    }).Where(s => s.ProductGroupId != null)
                                    .ToList();

                                var dispCatWiseSales = productSales.GroupBy(s => s.ProductGroupId).ToDictionary(gr => gr.Key, gr => gr.ToList());

                                var achievement = 0;
                                foreach (var sale in dispCatWiseSales)
                                {
                                    var productSalesList = sale.Value;
                                    var completedMoqCount = productSalesList.Count(s =>
                                        s.QuantitySales >= companyProducts.FirstOrDefault(p => p.Id == s.ProductId)?.MOQ);

                                    if (completedMoqCount >= 1)
                                    {
                                        achievement += 1;
                                    }
                                }

                                taskAchievementDict.Add(task.Id, ((double, double))(task.TaskTarget, achievement));

                                var upsertSoldQtyTasks = productIds.Select(productId =>
                                {
                                    var qtySale = productSales.Where(r => r.ProductId == productId).Sum(r => r.QuantitySales);
                                    var moq = companyProducts.FirstOrDefault(p => p.Id == productId)?.MOQ ?? 0;
                                    var productGroupId = companyProducts.FirstOrDefault(p => p.Id == productId)?.ProductGroupId ?? 0;

                                    return new TaskManagementSoldProductQuantity
                                    {
                                        TaskManagementTaskId = task.Id,
                                        ProductLevelHieararchyId = productId,
                                        TaskLevelHieararchyId = productGroupId,
                                        BilledQty = qtySale,
                                        RemainingBilledQty = Math.Max(moq - qtySale, 0),
                                        ShowTagInApp = false,
                                        CompanyId = companyId,
                                        CreatedAt = DateTime.UtcNow,
                                        LastUpdatedAt = DateTime.UtcNow,
                                        MOQ = moq
                                    };
                                }).ToList();

                                taskSoldProductQuantitesDict.Add(task.Id, upsertSoldQtyTasks);
                            }
                        }
                        break;
                }
                break;
        }

        if (taskAchievementDict.Count > 0)
        {
            await taskManagementRepository.UpdateTaskAchievement(companyId, taskAchievementDict);
            // add to data to save
            foreach (var taskAchievement in taskAchievementDict)
            {
                var entityId = filteredTasks.Where(s => s.Id == taskAchievement.Key).Select(s => s.TaskEntityId ?? 0).FirstOrDefault();
                var analyticData = new PerfectEntityRuleAnalytic
                {
                    CompanyId = companyId,
                    DateKey = todayDate.GetDateKey(),
                    PerfectEntityRuleId = ruleId,
                    CriteriaId = criteria.Id,
                    CriteriaType = criteria.CriteriaType.GetDisplayName(),
                    EntityId = entityId,
                    EntityType = entityType.GetDisplayName(),
                    ProductDivisionId = pdIds?.FirstOrDefault() ?? 0,
                    AchievementPercentage = taskAchievement.Value.Item2 / taskAchievement.Value.Item1 * 100,
                    Target = taskAchievement.Value.Item1.ToString(),
                    Achievement = taskAchievement.Value.Item2.ToString(),
                    CreatedAt = todayDate,
                };
                dataToSave.Add(analyticData);
            }
        }

        if (taskSoldProductQuantitesDict.Count > 0)
        {
            if (doCalculationToShowTagInApp)
            {
                switch (taskManagementFocusArea.TagLevel)
                {
                    case TagLevel.All:
                        foreach (var item in taskSoldProductQuantitesDict.Values.SelectMany(s => s))
                        {
                            item.ShowTagInApp = true;
                        }
                        break;
                    case TagLevel.TopBasedOnDistributorStock:
                        var outletBeatsData = await distributorRepository.GetBeatAndDistributorFromOutlets(companyId, outletIds);

                        var outletDistributorDict = outletBeatsData
                            .GroupBy(s => s.OutletId)
                            .ToDictionary(gr => gr.Key, gr => gr.Select(r => r.DistributorId).Distinct().ToList());

                        // Get all distributor IDs
                        var distributorIds = outletDistributorDict.Values.SelectMany(s => s).Distinct().ToList();

                        // Get distributor stock for the current date
                        var distributorStocks = await distributorRepository.GetDistributorStocksFromMaster(companyId, distributorIds);

                        foreach (var taskId in taskSoldProductQuantitesDict.Keys)
                        {
                            var soldProductsQuantities = taskSoldProductQuantitesDict[taskId];

                            // Group by TaskLevelHieararchyId
                            var groupedByHierarchy = soldProductsQuantities
                                .GroupBy(s => s.TaskLevelHieararchyId);

                            foreach (var hierarchyGroup in groupedByHierarchy)
                            {
                                var taskLevelHierarchyId = hierarchyGroup.Key;
                                var groupProducts = hierarchyGroup.ToList();
                                var productIds = groupProducts.Select(p => p.ProductLevelHieararchyId).Distinct().ToList();

                                // Filter distributor stock relevant to the products in this hierarchy group
                                var filteredDistributorStock = distributorStocks
                                    .Where(s => distributorIds.Contains(s.DistributorId))
                                    .SelectMany(s => s.Items)
                                    .Where(s => productIds.Contains(s.ProductId))
                                    .ToList();

                                var stockByProduct = filteredDistributorStock
                                    .GroupBy(s => s.ProductId)
                                    .ToDictionary(
                                        g => g.Key,
                                        g => g.Sum(s => s.StockValue)
                                    );

                                var maxStockProduct = stockByProduct
                                    .OrderByDescending(s => s.Value)
                                    .FirstOrDefault();

                                foreach (var soldProduct in groupProducts)
                                {
                                    soldProduct.DistributorStock = stockByProduct.TryGetValue(soldProduct.ProductLevelHieararchyId, out var stockVal)
                                                                    ? stockVal
                                                                    : 0;

                                    soldProduct.ShowTagInApp = soldProduct.ProductLevelHieararchyId == maxStockProduct.Key;
                                }
                            }
                        }

                        break;
                }
            }

            await taskManagementRepository.UpdateTaskSoldProductQuantites(companyId, taskSoldProductQuantitesDict);
        }

    }


    private async Task<List<ProductSales>> GetSales(long companyId, long outletId, long startDate, long endDate, SalesType saleType)
    {
        var productSalesForOutlet = new List<ProductSales>();
        switch (saleType)
        {
            case SalesType.Dispatch:
                productSalesForOutlet = await attendanceRepository.GetDispatchSalesProductWise(companyId, startDate, endDate, outletId);
                break;

            case SalesType.Invoice:
                productSalesForOutlet = await dmsInvoiceRepository.GetDMSSalesProductWise(companyId, startDate, endDate, outletId);
                break;

            case SalesType.NonFAInvoice:
                productSalesForOutlet = await nonFAInvoiceRepository.GetSalesProductWise(companyId, startDate, endDate, outletId);
                break;

            case SalesType.Order:
            default:
                productSalesForOutlet = await attendanceRepository.GetOutletSalesProductWise(companyId, startDate, endDate, outletId);
                break;

        }
        return productSalesForOutlet.Select(s => new ProductSales()
        {
            OutletId = s.OutletId,
            ProductId = s.ProductId,
            RevenueSales = s.RevenueSales,
            QuantitySales = s.QuantitySales,
            StdQuantitySales = s.StdQuantitySales
        }).ToList();

    }


    // Generic function to process all criteria types except TargetVsAchievement
    private async Task ProcessGeneralCriteria(
        long companyId,
        long entityId, 
        long ruleId,
        FilterConstraintEntityType entityType,
        long? pdId,
        PerfectEntityRuleCriteria criteria,
        DateTime todayDate,
        List<PerfectEntityRuleAnalytic> dataToSave)
    {
        var transactionalData = await perfectEntityTransactionalRepository.GetPerfectEntityCallDetails(
                companyId,
                criteria.Id,
                ruleId,
                todayDate.GetDateKey(),
                entityId,
                entityType
            );

        if (transactionalData == null)
        {
            return;
        }

        var achievementPercentage = (transactionalData?.IsPerfectCall ?? false) ? 100 : 0;

        var analyticData = CreateAnalyticData(companyId, entityId, pdId, ruleId, entityType, criteria, todayDate,
            achievementPercentage, transactionalData?.Target.ToString(), transactionalData.Achievement.ToString());

        var rewardId = await AssignRewards(companyId, criteria, analyticData);
        analyticData.RewardId = rewardId ?? 0;
        dataToSave.Add(analyticData);
    }

    private async Task ProcessTargetVsAchievementTypeCriteria(
        long companyId,
        long entityId,
        long? pdId,
        long ruleId,
        FilterConstraintEntityType entityType,
        PerfectEntityRuleCriteria criteria,
        DateTime todayDate,
        DateTime startDate,
        DateTime endDate,
        List<PerfectEntityRuleAnalytic> dataToSave)
    {
        var flexibleTarget = await companyTargetRepository.GetCompanyTargetsWithSubscriptions(
                companyId,
                entityId,
                pdId,
                criteria.CriteriaEntityId!.Value,
                startDate,
                endDate
            );

        if (flexibleTarget == null)
        {
            return;
        }

        var targetAchievement = await targetAchievementService.GetSingleTargetAch(
               companyId,
               flexibleTarget,
               startDate,
               todayDate
           );

        // Ensure proper achievement percentage calculation
        var achievementPercentage = targetAchievement?.Achievement != null ?
            (targetAchievement.Achievement.Value / flexibleTarget.TargetValue) * 100
            : 0;

        var analyticData = CreateAnalyticData(companyId, entityId, pdId, ruleId, entityType, criteria, todayDate,
                achievementPercentage, targetAchievement?.Target.ToString(), targetAchievement?.Achievement.ToString());

        var rewardId = await AssignRewards(companyId, criteria, analyticData);
        analyticData.RewardId = rewardId ?? 0;
        dataToSave.Add(analyticData);
    }

    // Helper method to create an analytic data object
    private static PerfectEntityRuleAnalytic CreateAnalyticData(
        long companyId,
        long entityId,
        long? pdId,
        long ruleId,
        FilterConstraintEntityType entityType,
        PerfectEntityRuleCriteria? criteria,
        DateTime todayDate,
        double? achievementPercentage,
        string? target,
        string? achievement)
    {
        return new PerfectEntityRuleAnalytic
        {
            CompanyId = companyId,
            PerfectEntityRuleId = ruleId,
            CriteriaId = criteria?.Id ?? 0,
            CriteriaType = criteria?.CriteriaType.GetDisplayName(),
            EntityId = entityId,
            EntityType = entityType.GetDisplayName(),
            ProductDivisionId = pdId ?? 0,
            DateKey = todayDate.GetDateKey(),
            AchievementPercentage = achievementPercentage ?? 0,
            Target = target,
            Achievement = achievement,
        };
    }

    // Assign rewards based on achievement percentage
    private async Task<long?> AssignRewards(
        long companyId,
        PerfectEntityRuleCriteria? criteria,
        PerfectEntityRuleAnalytic analyticData)
    {
        var slabs = criteria?.SlabDetails;

        if (slabs == null || slabs.Count == 0)
        {
            Console.WriteLine($"No slabs found for company {companyId} and criteria {criteria?.Id}");
            return null;
        }

        var achievementValue = double.TryParse(analyticData.Achievement ?? "0", out var parsedValue) ? parsedValue : 0;
        var weightageBasedAchievement = (analyticData.AchievementPercentage / 100) * criteria.Weightage;

        var slabTypeGroups = slabs.GroupBy(s => s.SlabCalculationType).ToList();


        // For a single criteria there can't be multiple slab calculation types
        if (slabTypeGroups.Count > 1)
        {
            Console.WriteLine($"Multiple slab calculation types found for criteria {criteria.Id}. Only one type per criteria is allowed.");
            return null;
        }

        var slabType = slabTypeGroups.First().Key;
        var rewardId = slabTypeGroups.ToDictionary(g => g.Key, g => g.Select(s => s.RewardId).FirstOrDefault()).Values.First();
        // Order based on sequence
        // This ensures that we always process slabs in the correct order
        var applicableSlabs = slabTypeGroups.First().OrderBy(s => s.Sequence).ToList();

        switch (slabType)
        {
            case SlabCalculationType.InfiniteStep:
                if (applicableSlabs.Count > 1)
                {
                    Console.WriteLine($"Multiple infinite step slabs found for criteria {criteria.Id}.");
                    return null;
                }

                var infiniteStepSlab = applicableSlabs[0];
                var slabStart = infiniteStepSlab.SlabStartPercentage;

                if (slabStart > 0)
                {
                    analyticData.RewardsAchieved = Math.Floor((achievementValue / slabStart) * infiniteStepSlab.RewardQuantity);
                }
                return infiniteStepSlab.RewardId;

            case SlabCalculationType.General:
                foreach (var slab in applicableSlabs)
                {
                    if (weightageBasedAchievement >= slab.SlabStartPercentage &&
                        (slab.SlabEndPercentage == null || weightageBasedAchievement <= slab.SlabEndPercentage))
                    {
                        analyticData.RewardsAchieved = (slab.SlabWeightage / 100) * slab.RewardQuantity;
                        return slab.RewardId;
                    }
                }
                break;

            case SlabCalculationType.ValueSlab:
                foreach (var slab in applicableSlabs)
                {
                    if (achievementValue >= slab.SlabStartPercentage &&
                        (slab.SlabEndPercentage == null || achievementValue <= slab.SlabEndPercentage))
                    {
                        analyticData.RewardsAchieved = slab.RewardQuantity;
                        return slab.RewardId;
                    }
                }
                break;

            case SlabCalculationType.SlabMultiplier:
                foreach (var slab in applicableSlabs)
                {
                    if (achievementValue >= slab.SlabStartPercentage &&
                        (slab.SlabEndPercentage == null || achievementValue <= slab.SlabEndPercentage))
                    {
                        analyticData.RewardsAchieved = achievementValue * slab.RewardQuantity;
                        return slab.RewardId;
                    }
                }
                break;
        }

        return rewardId;
    }
}
