﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations;

namespace PerfectStore.Core.Models;

public class Location
{
    public long Id { get; set; }
    public bool IsBlocked { get; set; }
    public string? ShopName { get; set; }
    public OutletSegmentation Segmentation { get; set; }
    public long CompanyId { get; set; }
    public string? ErpId { get; set; }
    public long? BeatId { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public string? ShopType { set; get; }
    public OutletChannel OutletChannel { set; get; }
    [StringLength(100)] public string? MarketName { set; get; }
    [StringLength(50)] public string? City { set; get; }
    [StringLength(200)] public string? SubCity { get; set; }
    [StringLength(200)] public string? District { get; set; }
    [StringLength(100)] public string? Country { get; set; }
    [StringLength(50)] public string? State { set; get; }
    public OutletSegmentation CompanySegmentation { set; get; }
    public bool IsFocused { set; get; }
    public string? ShopTypeCode { get; set; }
    [StringLength(50)] public string? AttributeText1 { get; set; }
    [StringLength(50)] public string? AttributeText2 { get; set; }
    [StringLength(50)] public string? AttributeText3 { get; set; }
    [StringLength(50)] public string? AttributeText4 { get; set; }
    public string? CustomTags { get; set; }
    public double? AttributeNumber1 { get; set; }
    public double? AttributeNumber2 { get; set; }
    public double? AttributeNumber3 { get; set; }
    public double? AttributeNumber4 { get; set; }
    public bool? AttributeBoolean1 { get; set; }
    public bool? AttributeBoolean2 { get; set; }
    public DateTime? AttributeDate1 { get; set; }
    public DateTime? AttributeDate2 { get; set; }
    public long? RegionId { get; set; }
    public long? ZoneId { get; set; }
    public long? TerritoryId { get; set; }
    public long? ShopTypeId { get; set; }

    public override bool Equals(object obj)
    {
        if (this == null)
        {
            return false;
        }

        return this.Equals(obj);
    }

    public override int GetHashCode()
    {
        throw new NotImplementedException();
    }
}