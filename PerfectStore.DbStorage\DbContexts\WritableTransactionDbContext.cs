﻿using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;

namespace PerfectStore.DbStorage.DbContexts
{
    public class WritableTransactionDbContext(
        DbContextOptions<WritableTransactionDbContext> options
    ) : DbContext(options)
    {
        public DbSet<PerfectStoreOutletStatus> PerfectStoreOutletStatus { get; set; }

        public DbSet<FocusAreaOutletStatus> FocusAreaOutletStatuses { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder) { }

        protected override void OnModelCreating(ModelBuilder modelBuilder) { }

        public override int SaveChanges()
        {
            throw new InvalidOperationException("use save changes async instead");
        }

        public override Task<int> SaveChangesAsync(
            CancellationToken cancellationToken = default(CancellationToken)
        )
        {
            return base.SaveChangesAsync(cancellationToken);
        }
    }
}
