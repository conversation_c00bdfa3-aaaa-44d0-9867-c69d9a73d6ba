﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.34929.205
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PerfectStoreProcessor", "PerfectStoreProcessor\PerfectStoreProcessor.csproj", "{F4FDD403-B374-4E47-A760-2F760F400712}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PerfectStore.Core", "PerfectStore.Core\PerfectStore.Core.csproj", "{C21C09B3-9718-4DD4-9789-1003BB909E10}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PerfectStore.DbStorage", "PerfectStore.DbStorage\PerfectStore.DbStorage.csproj", "{818F37D5-7D49-4C48-9FD3-6D860586B553}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PerfectStore.Tests", "PerfectStoreTests\PerfectStore.Tests.csproj", "{D75DD671-5B39-447D-AD51-5106F8FF424B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PerfectEntityProcessor", "PerfectEntityProcessor\PerfectEntityProcessor.csproj", "{A1639DD4-5D0F-4078-B6C7-C459D927A01F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PerfectEntityContinuousProcessor", "PerfectEntityContinuousProcessor\PerfectEntityContinuousProcessor.csproj", "{1750E8D5-ADBC-486D-AC0A-F13F223FA9BB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "FA_Libraries", "FA_Libraries", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.StringHelpers", "Lite_Library\Library.StringHelpers\Library.StringHelpers.csproj", "{8F068F79-A5B7-5891-7D95-36F884D18758}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EntityHelper", "Lite_Library\EntityHelper\EntityHelper.csproj", "{B190FC17-87DD-659A-A9DC-64D03680D2A0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FileGenerator", "Lite_Library\FileGenerator\FileGenerator.csproj", "{33F17AAE-2E7A-4F44-7E63-4C96BBF79CE8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AuditHelper", "Lite_Library\Libraries.Authentication\AuditHelper.csproj", "{F7B7F1E7-2C17-2561-A74A-252B2AE477A8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Libraries.Authorization", "Lite_Library\Libraries.Authorization\Libraries.Authorization.csproj", "{67C377B2-41D1-994E-CD73-847280FA1A8D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Libraries.CacheProvider", "Lite_Library\Libraries.CacheProvider\Libraries.CacheProvider.csproj", "{0316F9B0-0667-EFB6-16BC-89819DC5C83F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Libraries.CommonEnums", "Lite_Library\Libraries.CommonEnums\Libraries.CommonEnums.csproj", "{A7822C7B-C96F-832C-9DE3-680F5F63C834}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Libraries.CommonModels", "Lite_Library\Libraries.CommonModels\Libraries.CommonModels.csproj", "{F19E5601-0CA0-7D01-06C5-C461D063812C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Libraries.CosmosV3", "Lite_Library\Libraries.CosmosV3\Libraries.CosmosV3.csproj", "{8FD8871C-04BA-C2C3-F284-4A6E0597AE1E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Libraries.Cryptography", "Lite_Library\Libraries.Cryptography\Libraries.Cryptography.csproj", "{F2EACC2D-EC80-2A30-B607-B2E3F9371494}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Libraries.PerspectiveColumns", "Lite_Library\Libraries.PerspectiveColumns\Libraries.PerspectiveColumns.csproj", "{DFF5130E-D7EE-F0FB-D18F-C1745F146DC5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.ApiLogger", "Lite_Library\Library.ApiLogger\Library.ApiLogger.csproj", "{B087116C-7A08-813D-DC6C-474FD660B793}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.AsyncLock", "Lite_Library\Library.AsyncLock\Library.AsyncLock.csproj", "{C7CD34C6-EAAE-851F-9FCD-70947457DF11}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.CommonHelpers", "Lite_Library\Library.CommonHelpers\Library.CommonHelpers.csproj", "{C8693A28-A77A-B66D-32C0-1684111174D0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.ConnectionStringParsor", "Lite_Library\Library.ConnectionStringParsor\Library.ConnectionStringParsor.csproj", "{B8249E29-B9BC-5A71-2883-2D362DFEBCB5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.DateTimeHelpers", "Lite_Library\Library.DateTimeHelpers\Library.DateTimeHelpers.csproj", "{DCDF8E99-6114-BD5A-E4B4-63ACE7C1129E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.EmailService", "Lite_Library\Library.EmailService\Library.EmailService.csproj", "{3240C543-BB29-6E23-D322-B077A201780C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.Encryption", "Lite_Library\Library.Encryption\Library.Encryption.csproj", "{09830D9F-**************-AA67EC7421CE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.FaExceptions", "Lite_Library\Library.FaExceptions\Library.FaExceptions.csproj", "{437FA632-F680-B727-F673-B9E5BBC8192F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.Geocodes", "Lite_Library\Library.Geocodes\Library.Geocodes.csproj", "{60AD9E91-3F65-9E40-6A13-115751609346}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.GuidGenerator", "Lite_Library\Library.GuidGenerator\Library.GuidGenerator.csproj", "{0394ABA4-69AB-4F58-B455-F43165F07615}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.Infrastructure", "Lite_Library\Library.Infrastructure\Library.Infrastructure.csproj", "{AF48F6EF-63CC-3A81-7008-82C4FAB94F43}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.JsonHelper", "Lite_Library\Library.JsonHelper\Library.JsonHelper.csproj", "{2C7885D7-5C84-5B81-B5A6-1ACE1649F7C8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.NetworkingHelper", "Lite_Library\Library.NetworkingHelper\Library.NetworkingHelper.csproj", "{7114B8EB-EAF4-B8B0-59C3-7EAE1D668EBD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.NumberSystem", "Lite_Library\Library.NumberSystem\Library.NumberSystem.csproj", "{58DF80D3-535C-8F37-40CE-F9658392615A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.PaymentService", "Lite_Library\Library.PaymentService\Library.PaymentService.csproj", "{F642389F-6780-812E-25EC-8AE0C821038C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.ResiliencyHelpers", "Lite_Library\Library.ResiliencyHelpers\Library.ResiliencyHelpers.csproj", "{761B0433-454B-266E-452A-C029AFD86E69}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.ResilientHttpClient", "Lite_Library\Library.ResilientHttpClient\Library.ResilientHttpClient.csproj", "{78788DA7-F757-1C05-7C14-061E4230C046}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.ResponseHelpers", "Lite_Library\Library.ResponseHelpers\Library.ResponseHelpers.csproj", "{3E81D677-45ED-8861-89F0-78098AC374A0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.ReverseGeoCoder", "Lite_Library\Library.ReverseGeoCoder\Library.ReverseGeoCoder.csproj", "{F1153C3A-6035-95AB-47FD-9CCD30A915AC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.SlackService", "Lite_Library\Library.SlackService\Library.SlackService.csproj", "{CA4B6D6E-874C-EB6F-91CE-CB431D87A0DA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.SMSHelpers", "Lite_Library\Library.SMSHelpers\Library.SMSHelpers.csproj", "{9E2FDCAA-8B8A-80C5-9EF1-72B3105381EF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.StorageWriter", "Lite_Library\Library.StorageWriter\Library.StorageWriter.csproj", "{0014D456-3B46-9617-DFE9-2106D8B4753F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Library.SqlHelper", "Lite_Library\Library.SqlHelper\Library.SqlHelper.csproj", "{C1E3588E-09A0-0181-AC4F-24D8B96702AA}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F4FDD403-B374-4E47-A760-2F760F400712}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F4FDD403-B374-4E47-A760-2F760F400712}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F4FDD403-B374-4E47-A760-2F760F400712}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F4FDD403-B374-4E47-A760-2F760F400712}.Release|Any CPU.Build.0 = Release|Any CPU
		{C21C09B3-9718-4DD4-9789-1003BB909E10}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C21C09B3-9718-4DD4-9789-1003BB909E10}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C21C09B3-9718-4DD4-9789-1003BB909E10}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C21C09B3-9718-4DD4-9789-1003BB909E10}.Release|Any CPU.Build.0 = Release|Any CPU
		{818F37D5-7D49-4C48-9FD3-6D860586B553}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{818F37D5-7D49-4C48-9FD3-6D860586B553}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{818F37D5-7D49-4C48-9FD3-6D860586B553}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{818F37D5-7D49-4C48-9FD3-6D860586B553}.Release|Any CPU.Build.0 = Release|Any CPU
		{D75DD671-5B39-447D-AD51-5106F8FF424B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D75DD671-5B39-447D-AD51-5106F8FF424B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D75DD671-5B39-447D-AD51-5106F8FF424B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D75DD671-5B39-447D-AD51-5106F8FF424B}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1639DD4-5D0F-4078-B6C7-C459D927A01F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1639DD4-5D0F-4078-B6C7-C459D927A01F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1639DD4-5D0F-4078-B6C7-C459D927A01F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1639DD4-5D0F-4078-B6C7-C459D927A01F}.Release|Any CPU.Build.0 = Release|Any CPU
		{1750E8D5-ADBC-486D-AC0A-F13F223FA9BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1750E8D5-ADBC-486D-AC0A-F13F223FA9BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1750E8D5-ADBC-486D-AC0A-F13F223FA9BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1750E8D5-ADBC-486D-AC0A-F13F223FA9BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{8F068F79-A5B7-5891-7D95-36F884D18758}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8F068F79-A5B7-5891-7D95-36F884D18758}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8F068F79-A5B7-5891-7D95-36F884D18758}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8F068F79-A5B7-5891-7D95-36F884D18758}.Release|Any CPU.Build.0 = Release|Any CPU
		{B190FC17-87DD-659A-A9DC-64D03680D2A0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B190FC17-87DD-659A-A9DC-64D03680D2A0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B190FC17-87DD-659A-A9DC-64D03680D2A0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B190FC17-87DD-659A-A9DC-64D03680D2A0}.Release|Any CPU.Build.0 = Release|Any CPU
		{33F17AAE-2E7A-4F44-7E63-4C96BBF79CE8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{33F17AAE-2E7A-4F44-7E63-4C96BBF79CE8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{33F17AAE-2E7A-4F44-7E63-4C96BBF79CE8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{33F17AAE-2E7A-4F44-7E63-4C96BBF79CE8}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7B7F1E7-2C17-2561-A74A-252B2AE477A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7B7F1E7-2C17-2561-A74A-252B2AE477A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7B7F1E7-2C17-2561-A74A-252B2AE477A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7B7F1E7-2C17-2561-A74A-252B2AE477A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{67C377B2-41D1-994E-CD73-847280FA1A8D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{67C377B2-41D1-994E-CD73-847280FA1A8D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{67C377B2-41D1-994E-CD73-847280FA1A8D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{67C377B2-41D1-994E-CD73-847280FA1A8D}.Release|Any CPU.Build.0 = Release|Any CPU
		{0316F9B0-0667-EFB6-16BC-89819DC5C83F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0316F9B0-0667-EFB6-16BC-89819DC5C83F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0316F9B0-0667-EFB6-16BC-89819DC5C83F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0316F9B0-0667-EFB6-16BC-89819DC5C83F}.Release|Any CPU.Build.0 = Release|Any CPU
		{A7822C7B-C96F-832C-9DE3-680F5F63C834}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7822C7B-C96F-832C-9DE3-680F5F63C834}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7822C7B-C96F-832C-9DE3-680F5F63C834}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7822C7B-C96F-832C-9DE3-680F5F63C834}.Release|Any CPU.Build.0 = Release|Any CPU
		{F19E5601-0CA0-7D01-06C5-C461D063812C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F19E5601-0CA0-7D01-06C5-C461D063812C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F19E5601-0CA0-7D01-06C5-C461D063812C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F19E5601-0CA0-7D01-06C5-C461D063812C}.Release|Any CPU.Build.0 = Release|Any CPU
		{8FD8871C-04BA-C2C3-F284-4A6E0597AE1E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8FD8871C-04BA-C2C3-F284-4A6E0597AE1E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8FD8871C-04BA-C2C3-F284-4A6E0597AE1E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8FD8871C-04BA-C2C3-F284-4A6E0597AE1E}.Release|Any CPU.Build.0 = Release|Any CPU
		{F2EACC2D-EC80-2A30-B607-B2E3F9371494}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F2EACC2D-EC80-2A30-B607-B2E3F9371494}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F2EACC2D-EC80-2A30-B607-B2E3F9371494}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F2EACC2D-EC80-2A30-B607-B2E3F9371494}.Release|Any CPU.Build.0 = Release|Any CPU
		{DFF5130E-D7EE-F0FB-D18F-C1745F146DC5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DFF5130E-D7EE-F0FB-D18F-C1745F146DC5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DFF5130E-D7EE-F0FB-D18F-C1745F146DC5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DFF5130E-D7EE-F0FB-D18F-C1745F146DC5}.Release|Any CPU.Build.0 = Release|Any CPU
		{B087116C-7A08-813D-DC6C-474FD660B793}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B087116C-7A08-813D-DC6C-474FD660B793}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B087116C-7A08-813D-DC6C-474FD660B793}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B087116C-7A08-813D-DC6C-474FD660B793}.Release|Any CPU.Build.0 = Release|Any CPU
		{C7CD34C6-EAAE-851F-9FCD-70947457DF11}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C7CD34C6-EAAE-851F-9FCD-70947457DF11}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C7CD34C6-EAAE-851F-9FCD-70947457DF11}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C7CD34C6-EAAE-851F-9FCD-70947457DF11}.Release|Any CPU.Build.0 = Release|Any CPU
		{C8693A28-A77A-B66D-32C0-1684111174D0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C8693A28-A77A-B66D-32C0-1684111174D0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C8693A28-A77A-B66D-32C0-1684111174D0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C8693A28-A77A-B66D-32C0-1684111174D0}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8249E29-B9BC-5A71-2883-2D362DFEBCB5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8249E29-B9BC-5A71-2883-2D362DFEBCB5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8249E29-B9BC-5A71-2883-2D362DFEBCB5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8249E29-B9BC-5A71-2883-2D362DFEBCB5}.Release|Any CPU.Build.0 = Release|Any CPU
		{DCDF8E99-6114-BD5A-E4B4-63ACE7C1129E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DCDF8E99-6114-BD5A-E4B4-63ACE7C1129E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DCDF8E99-6114-BD5A-E4B4-63ACE7C1129E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DCDF8E99-6114-BD5A-E4B4-63ACE7C1129E}.Release|Any CPU.Build.0 = Release|Any CPU
		{3240C543-BB29-6E23-D322-B077A201780C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3240C543-BB29-6E23-D322-B077A201780C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3240C543-BB29-6E23-D322-B077A201780C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3240C543-BB29-6E23-D322-B077A201780C}.Release|Any CPU.Build.0 = Release|Any CPU
		{09830D9F-**************-AA67EC7421CE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{09830D9F-**************-AA67EC7421CE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{09830D9F-**************-AA67EC7421CE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{09830D9F-**************-AA67EC7421CE}.Release|Any CPU.Build.0 = Release|Any CPU
		{437FA632-F680-B727-F673-B9E5BBC8192F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{437FA632-F680-B727-F673-B9E5BBC8192F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{437FA632-F680-B727-F673-B9E5BBC8192F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{437FA632-F680-B727-F673-B9E5BBC8192F}.Release|Any CPU.Build.0 = Release|Any CPU
		{60AD9E91-3F65-9E40-6A13-115751609346}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{60AD9E91-3F65-9E40-6A13-115751609346}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{60AD9E91-3F65-9E40-6A13-115751609346}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{60AD9E91-3F65-9E40-6A13-115751609346}.Release|Any CPU.Build.0 = Release|Any CPU
		{0394ABA4-69AB-4F58-B455-F43165F07615}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0394ABA4-69AB-4F58-B455-F43165F07615}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0394ABA4-69AB-4F58-B455-F43165F07615}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0394ABA4-69AB-4F58-B455-F43165F07615}.Release|Any CPU.Build.0 = Release|Any CPU
		{AF48F6EF-63CC-3A81-7008-82C4FAB94F43}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF48F6EF-63CC-3A81-7008-82C4FAB94F43}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF48F6EF-63CC-3A81-7008-82C4FAB94F43}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF48F6EF-63CC-3A81-7008-82C4FAB94F43}.Release|Any CPU.Build.0 = Release|Any CPU
		{2C7885D7-5C84-5B81-B5A6-1ACE1649F7C8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2C7885D7-5C84-5B81-B5A6-1ACE1649F7C8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2C7885D7-5C84-5B81-B5A6-1ACE1649F7C8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2C7885D7-5C84-5B81-B5A6-1ACE1649F7C8}.Release|Any CPU.Build.0 = Release|Any CPU
		{7114B8EB-EAF4-B8B0-59C3-7EAE1D668EBD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7114B8EB-EAF4-B8B0-59C3-7EAE1D668EBD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7114B8EB-EAF4-B8B0-59C3-7EAE1D668EBD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7114B8EB-EAF4-B8B0-59C3-7EAE1D668EBD}.Release|Any CPU.Build.0 = Release|Any CPU
		{58DF80D3-535C-8F37-40CE-F9658392615A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{58DF80D3-535C-8F37-40CE-F9658392615A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{58DF80D3-535C-8F37-40CE-F9658392615A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{58DF80D3-535C-8F37-40CE-F9658392615A}.Release|Any CPU.Build.0 = Release|Any CPU
		{F642389F-6780-812E-25EC-8AE0C821038C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F642389F-6780-812E-25EC-8AE0C821038C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F642389F-6780-812E-25EC-8AE0C821038C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F642389F-6780-812E-25EC-8AE0C821038C}.Release|Any CPU.Build.0 = Release|Any CPU
		{761B0433-454B-266E-452A-C029AFD86E69}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{761B0433-454B-266E-452A-C029AFD86E69}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{761B0433-454B-266E-452A-C029AFD86E69}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{761B0433-454B-266E-452A-C029AFD86E69}.Release|Any CPU.Build.0 = Release|Any CPU
		{78788DA7-F757-1C05-7C14-061E4230C046}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{78788DA7-F757-1C05-7C14-061E4230C046}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{78788DA7-F757-1C05-7C14-061E4230C046}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{78788DA7-F757-1C05-7C14-061E4230C046}.Release|Any CPU.Build.0 = Release|Any CPU
		{3E81D677-45ED-8861-89F0-78098AC374A0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E81D677-45ED-8861-89F0-78098AC374A0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3E81D677-45ED-8861-89F0-78098AC374A0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3E81D677-45ED-8861-89F0-78098AC374A0}.Release|Any CPU.Build.0 = Release|Any CPU
		{F1153C3A-6035-95AB-47FD-9CCD30A915AC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F1153C3A-6035-95AB-47FD-9CCD30A915AC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F1153C3A-6035-95AB-47FD-9CCD30A915AC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F1153C3A-6035-95AB-47FD-9CCD30A915AC}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA4B6D6E-874C-EB6F-91CE-CB431D87A0DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA4B6D6E-874C-EB6F-91CE-CB431D87A0DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA4B6D6E-874C-EB6F-91CE-CB431D87A0DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA4B6D6E-874C-EB6F-91CE-CB431D87A0DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{9E2FDCAA-8B8A-80C5-9EF1-72B3105381EF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9E2FDCAA-8B8A-80C5-9EF1-72B3105381EF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9E2FDCAA-8B8A-80C5-9EF1-72B3105381EF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9E2FDCAA-8B8A-80C5-9EF1-72B3105381EF}.Release|Any CPU.Build.0 = Release|Any CPU
		{0014D456-3B46-9617-DFE9-2106D8B4753F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0014D456-3B46-9617-DFE9-2106D8B4753F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0014D456-3B46-9617-DFE9-2106D8B4753F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0014D456-3B46-9617-DFE9-2106D8B4753F}.Release|Any CPU.Build.0 = Release|Any CPU
		{C1E3588E-09A0-0181-AC4F-24D8B96702AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C1E3588E-09A0-0181-AC4F-24D8B96702AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C1E3588E-09A0-0181-AC4F-24D8B96702AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C1E3588E-09A0-0181-AC4F-24D8B96702AA}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{8F068F79-A5B7-5891-7D95-36F884D18758} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{B190FC17-87DD-659A-A9DC-64D03680D2A0} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{33F17AAE-2E7A-4F44-7E63-4C96BBF79CE8} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{F7B7F1E7-2C17-2561-A74A-252B2AE477A8} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{67C377B2-41D1-994E-CD73-847280FA1A8D} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{0316F9B0-0667-EFB6-16BC-89819DC5C83F} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{A7822C7B-C96F-832C-9DE3-680F5F63C834} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{F19E5601-0CA0-7D01-06C5-C461D063812C} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{8FD8871C-04BA-C2C3-F284-4A6E0597AE1E} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{F2EACC2D-EC80-2A30-B607-B2E3F9371494} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{DFF5130E-D7EE-F0FB-D18F-C1745F146DC5} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{B087116C-7A08-813D-DC6C-474FD660B793} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{C7CD34C6-EAAE-851F-9FCD-70947457DF11} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{C8693A28-A77A-B66D-32C0-1684111174D0} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{B8249E29-B9BC-5A71-2883-2D362DFEBCB5} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{DCDF8E99-6114-BD5A-E4B4-63ACE7C1129E} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{3240C543-BB29-6E23-D322-B077A201780C} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{09830D9F-**************-AA67EC7421CE} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{437FA632-F680-B727-F673-B9E5BBC8192F} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{60AD9E91-3F65-9E40-6A13-115751609346} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{0394ABA4-69AB-4F58-B455-F43165F07615} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{AF48F6EF-63CC-3A81-7008-82C4FAB94F43} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{2C7885D7-5C84-5B81-B5A6-1ACE1649F7C8} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{7114B8EB-EAF4-B8B0-59C3-7EAE1D668EBD} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{58DF80D3-535C-8F37-40CE-F9658392615A} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{F642389F-6780-812E-25EC-8AE0C821038C} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{761B0433-454B-266E-452A-C029AFD86E69} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{78788DA7-F757-1C05-7C14-061E4230C046} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{3E81D677-45ED-8861-89F0-78098AC374A0} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{F1153C3A-6035-95AB-47FD-9CCD30A915AC} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{CA4B6D6E-874C-EB6F-91CE-CB431D87A0DA} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{9E2FDCAA-8B8A-80C5-9EF1-72B3105381EF} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{0014D456-3B46-9617-DFE9-2106D8B4753F} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{C1E3588E-09A0-0181-AC4F-24D8B96702AA} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E9A77FE0-AC10-46BB-9D42-BE20D92130BA}
	EndGlobalSection
EndGlobal
