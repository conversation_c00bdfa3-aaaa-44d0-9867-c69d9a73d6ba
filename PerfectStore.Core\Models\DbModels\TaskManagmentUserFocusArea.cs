﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations;

namespace PerfectStore.Core.Models.DbModels
{
    public class TaskManagementUserFocusArea
    {
        public long Id { get; set; }

        public long? TaskManagementFocusAreaID { get; set; }

        public long CompanyId { get; set; }

        [StringLength(64)]
        public string TargetQuery { get; set; }

        [StringLength(256)]
        public string AchievementQuery { get; set; }

        public long? EntityId { get; set; }

        [StringLength(64)]
        public string Description { get; set; }

        public bool IsDeactive { get; set; }

        public DateTime CreatedAt { get; set; }

        [StringLength(32)]
        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        [StringLength(32)]
        public string TargetUIName { get; set; }

        public TaskManagementQueryRelation TargetQueryDB { get; set; }

        [StringLength(64)]
        public string AchievementUIName { get; set; }

        public TaskManagementQueryRelation AchievementQueryDB { get; set; }

        [StringLength(64)]
        public string Instruction { get; set; }

        [StringLength(32)]
        public string TaskTitle { get; set; }

        public UIElementType UIElementType { get; set; }

        public TaskManagementFocusArea TaskManagementFocusArea { get; set; }
    }
}
