﻿using System.ComponentModel.DataAnnotations.Schema;

namespace PerfectStore.Core.Models.DbModels;

public class Distributor
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public long? DistributorChannelId { get; set; }

    public long? DistributorSegmentationId { get; set; }

    public bool Deleted { get; set; }

    public long? RegionId { get; set; }

    [Column("ZoneId")]
    public long? ZoneId { get; set; }
}

public class DistributorBeatMappingDB
{
    public long Id { get; set; }

    public long DistributorId { get; set; }

    [ForeignKey("LocationBeat")]
    public long BeatId { get; set; }

    public DateTime CreatedAt { get; set; }

    public string CreationContext { get; set; }

    [ForeignKey("Company")]
    public long CompanyId { get; set; }

    public bool Deleted { get; set; }

    public Distributor Distributor { get; set; }

    public LocationBeat Beat { get; set; }

    public DateTime LastUpdatedAt { get; set; }
}