﻿using Libraries.CommonModels;
using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.MasterRepositories;

public class LocationRepository(MasterDbContext masterDbContext) : ILocationRepository
{
    public async Task<List<Location>> GetOutletsForCompany(long companyId)
    {
        var data = await masterDbContext
            .Locations.Where(s => s.CompanyId == companyId && !s.IsBlocked)
            .Select(l => new Location
            {
                AttributeBoolean1 = l.AttributeBoolean1,
                AttributeBoolean2 = l.AttributeBoolean2,
                AttributeDate1 = l.AttributeDate1,
                AttributeDate2 = l.AttributeDate2,
                AttributeNumber1 = l.AttributeNumber1,
                AttributeNumber2 = l.AttributeNumber2,
                AttributeNumber3 = l.AttributeNumber3,
                AttributeNumber4 = l.AttributeNumber4,
                AttributeText1 = l.AttributeText1,
                AttributeText2 = l.AttributeText2,
                AttributeText3 = l.AttributeText3,
                AttributeText4 = l.AttributeText4,
                BeatId = l.BeatId,
                City = l.City,
                CompanyId = l.CompanyId,
                CompanySegmentation = l.CompanySegmentation,
                Country = l.Country,
                CustomTags = l.CustomTags,
                District = l.District,
                ErpId = l.ErpId,
                Id = l.Id,
                IsBlocked = l.IsBlocked,
                IsFocused = l.IsFocused,
                Latitude = l.Latitude,
                Longitude = l.Longitude,
                MarketName = l.MarketName,
                ShopName = l.ShopName,
                Segmentation = l.Segmentation,
                ShopTypeCode = l.ShopTypeCode,
                State = l.State,
                SubCity = l.SubCity,
                OutletChannel = l.OutletChannel,
                TerritoryId = l.Beat.TerritoryId,
                RegionId = l.Beat.Territory.RegionId,
                ZoneId = l.Beat.Territory.TheRegion.ZoneId,
                ShopTypeId = l.ShopTypeId
            })
            .ToListAsync();

        return data ?? new List<Location>();
    }
}
