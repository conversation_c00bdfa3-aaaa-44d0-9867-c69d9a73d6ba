﻿using Libraries.CommonEnums;

namespace PerfectStore.Core.Models.DbModels.TransactionDbModels;

public class PerfectEntityCallDetail
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public DateTime CreatedAt { get; set; }

    public string? CreationContext { get; set; }

    public long DateKey { get; set; }

    public long FAEventId { get; set; }

    public long PerfectEntityRuleId { get; set; }

    public long CriteriaId { get; set; }

    public TaskManagementFocusAreaType CriteriaType { get; set; }

    public long EntityId { get; set; }

    public FilterConstraintEntityType EntityType { get; set; }

    public bool IsPerfectCall { get; set; }

    public double? Target { get; set; }

    public double? Achievement { get; set; } 
}
