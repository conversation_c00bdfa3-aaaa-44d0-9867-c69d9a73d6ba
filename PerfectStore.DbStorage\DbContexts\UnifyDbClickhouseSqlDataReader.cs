﻿using Library.SqlHelper;

namespace PerfectStore.DbStorage.DbContexts;

public class UnifyDbClickhouseSqlDataReader : ClickHouseSqldataReader
{
    public UnifyDbClickhouseSqlDataReader(
        string connectionString,
        IHttpClientFactory httpClientFactory,
        string httpClientName = SuggestedHttpClientName,
        int commandTimeout = 30
    )
        : base(connectionString, httpClientFactory, httpClientName, commandTimeout, "unify") { }
}


public class ReadOnlyUnifyDbClickhouseSqlDataReader : ClickHouseSqldataReader
{
    public ReadOnlyUnifyDbClickhouseSqlDataReader(
        string connectionString,
        IHttpClientFactory httpClientFactory,
        string httpClientName = SuggestedHttpClientName,
        int commandTimeout = 30
    )
        : base(connectionString, httpClientFactory, httpClientName, commandTimeout, "unify") { }
}