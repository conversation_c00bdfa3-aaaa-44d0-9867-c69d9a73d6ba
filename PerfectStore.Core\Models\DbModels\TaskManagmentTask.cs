﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace PerfectStore.Core.Models.DbModels
{ 
    public class TaskManagementTask
    {
        public long Id { get; set; }

        public long? TaskManagementFocusAreaID { get; set; }

        public FlexibleTargetEntityType? TaskEntityType { get; set; }

        public long? TaskEntityId { get; set; }

        public long? TaskTarget { get; set; }

        public long? Sequence { get; set; }

        public ProductLevelHierarchy? ProductHierarchyLevel { get; set; }

        public string? ProductHierarchyIds { get; set; }

        [NotMapped]
        public List<long>? ProductHierarchyIdsList => !string.IsNullOrEmpty(ProductHierarchyIds) ? JsonSerializer.Deserialize<List<long>>(ProductHierarchyIds) : null;

        public string? TaskLevelHieararchyIds { get; set; }

        [NotMapped]
        public List<long>? TaskLevelHierarchyIdsList => !string.IsNullOrEmpty(TaskLevelHieararchyIds) ? JsonSerializer.Deserialize<List<long>>(TaskLevelHieararchyIds) : null;

        public CalculationMeasure? CalculationMeasure { get; set; }

        public string? Description { get; set; }

        public bool? IsDeactive { get; set; }

        public DateTime? DeactivatedAt { get; set; }

        public long? RewardId { get; set; }

        public int? RewardQuantity { get; set; }

        public Reward FARewards { get; set; }

        public TaskManagementFocusArea TaskManagementFocusAreas { get; set; }

        public long CompanyId { get; set; }

        public string? ProductSuggestedQtyList { get; set; }

        public long? Achievement { get; set; }

        [NotMapped]
        public List<ProductSuggestedQtyDto>? ProductSuggestedQtyListDto => !string.IsNullOrEmpty(ProductSuggestedQtyList) ? JsonSerializer.Deserialize<List<ProductSuggestedQtyDto>>(ProductSuggestedQtyList) : null;
    }

    public class ProductSuggestedQtyDto
    {
        public long ProductHierarchyId { get; set; }

        public double SuggestedQty { get; set; }
    }
}
