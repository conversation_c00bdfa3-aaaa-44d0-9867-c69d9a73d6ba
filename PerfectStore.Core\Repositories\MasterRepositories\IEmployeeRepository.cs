﻿using Libraries.CommonEnums;
using Libraries.CommonModels;

namespace PerfectStore.Core.Repositories.MasterRepositories;

public interface IEmployeeRepository
{
    Task<List<EmployeeMinWithRank>> GetFieldUserIdsUnderManagerModel(
        long companyId,
        PortalUserRole userRole,
        List<long> userIds,
        EmployeeType? userType = null
    );

    Task<List<EmployeeMinWithType>> GetAllEmployees(long companyId);

    Task<List<EmployeeMinWithType>> GetAllEmployeesSRAndDSR(long companyId);
}
