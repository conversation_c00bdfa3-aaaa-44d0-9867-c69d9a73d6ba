﻿using Library.CommonHelpers;
using Library.SqlHelper;
using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Repositories.UnifyClickhouseRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.UnifyClickhouseRepositories;

public class NonFAInvoiceRepository(UnifyDbClickhouseSqlDataReader unifyDbClickhouseSqlDataReader) : INonFAInvoiceRepository
{
    public async Task<List<ProductSales>> GetSalesProductWise(long companyId, long startDate, long endDate, long outletId)
    {
        var datatable = await unifyDbClickhouseSqlDataReader.GetDatatableForQuery(
                    $@"SELECT 
                            CAST(SUM(OrderInRevenue) AS Float64) AS RevenueSales,
                            CAST(SUM(OrderInStdUnits * StandardUnitConversionFactor) AS Float64) AS QuantitySales,
                            CAST(SUM(OrderInStdUnits) AS Float64) AS StdQuantitySales,
                            ProductId,
                            LocationId AS OutletId
                        FROM NonFAInvoiceDetail
                        WHERE 
                            CompanyId = {companyId} 
                            AND InvoiceDateKey BETWEEN {startDate} AND {endDate}
                            AND LocationId = {outletId}
                        GROUP BY ProductId, LocationId");
        var dataList = DataTableToObjectModelConverter<ProductSales>.Convert(datatable);
        return dataList;
    }


}
