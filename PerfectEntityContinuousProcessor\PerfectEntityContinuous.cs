using Amazon.Runtime.Internal.Util;
using Microsoft.Azure.WebJobs;
using PerfectStore.Core.Helpers.QueueHandlers;
using PerfectStore.Core.Services;
using Serilog;
using System.Text.Json;

namespace PerfectEntityContinuousProcessor;

public class PerfectEntityContinuous
{
    private readonly IPerfectEntityContinuousService _perfectEntityContinuousService;

    public PerfectEntityContinuous(IPerfectEntityContinuousService perfectEntityContinuousService)
    {
        _perfectEntityContinuousService = perfectEntityContinuousService;
    }

    public async Task ProcessQueueAsync(
        [QueueTrigger("perfectentityrule-queue", Connection = "StorageConnectionString")]
            PerfectEntityRuleQueueData data
    )
    {
        try
        {
            Log.Information($"Processor starts running for data {JsonSerializer.Serialize(data)}");
            await _perfectEntityContinuousService.ProcessQueueAsync(data);
            Log.Information($"Processor ends running for data {JsonSerializer.Serialize(data)}");
        }
        catch (Exception ex)
        {
            Log.Error("Failed", ex);
            Console.WriteLine(ex);
            throw;
        }
    }
}
