using Libraries.CommonEnums;
using PerfectStore.Core.Helpers.QueueHandlers;
using PerfectStore.Core.Models.DbModels;

namespace PerfectStore.Tests.Helpers;

/// <summary>
/// Helper class for generating test data for unit tests
/// </summary>
public static class TestDataGenerator
{
    /// <summary>
    /// Creates a PerfectEntityRuleQueueData object with default values
    /// </summary>
    public static PerfectEntityRuleQueueData CreatePerfectEntityRuleQueueData(
        long? companyId = null,
        long? ruleId = null,
        long? entityId = null,
        FilterConstraintEntityType? entityType = null,
        long? productDivisionId = null,
        DateTime? date = null)
    {
        return new PerfectEntityRuleQueueData
        {
            CompanyId = companyId ?? 10580,
            RuleId = ruleId ?? 1,
            Date = date ?? DateTime.Now,
            EntityIdBlobPath = "test-blob-path",
            ContainerName = "test-container",
        };
    }
}
