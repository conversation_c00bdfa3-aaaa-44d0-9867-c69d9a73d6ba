# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS base
WORKDIR /app
ENV DOTNET_RUNNING_IN_CONTAINER=true
RUN apk add --no-cache icu-libs
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false

# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["PerfectStoreProcessor/PerfectStoreProcessor.csproj", "PerfectStoreProcessor/"]
COPY ["FA_Libraries/Library.ConnectionStringParsor/Library.ConnectionStringParsor.csproj", "FA_Libraries/Library.ConnectionStringParsor/"]
COPY ["FA_Libraries/Library.ResiliencyHelpers/Library.ResiliencyHelpers.csproj", "FA_Libraries/Library.ResiliencyHelpers/"]
COPY ["FA_Libraries/Library.SlackService/Library.SlackService.csproj", "FA_Libraries/Library.SlackService/"]
COPY ["FA_Libraries/Libraries.CommonEnums/Libraries.CommonEnums.csproj", "FA_Libraries/Libraries.CommonEnums/"]
COPY ["FA_Libraries/Library.Infrastructure/Library.Infrastructure.csproj", "FA_Libraries/Library.Infrastructure/"]
COPY ["FA_Libraries/Library.StorageWriter/Library.StorageWriter.csproj", "FA_Libraries/Library.StorageWriter/"]
COPY ["FA_Libraries/Libraries.Cryptography/Libraries.Cryptography.csproj", "FA_Libraries/Libraries.Cryptography/"]
COPY ["PerfectStore.Core/PerfectStore.Core.csproj", "PerfectStore.Core/"]
COPY ["FA_Libraries/Library.DateTimeHelpers/Library.DateTimeHelpers.csproj", "FA_Libraries/Library.DateTimeHelpers/"]
COPY ["FA_Libraries/Library.FaExceptions/Library.FaExceptions.csproj", "FA_Libraries/Library.FaExceptions/"]
COPY ["FA_Libraries/Library.CommonHelpers/Library.CommonHelpers.csproj", "FA_Libraries/Library.CommonHelpers/"]
COPY ["FA_Libraries/Library.NumberSystem/Library.NumberSystem.csproj", "FA_Libraries/Library.NumberSystem/"]
COPY ["FA_Libraries/Library.ResilientHttpClient/Library.ResilientHttpClient.csproj", "FA_Libraries/Library.ResilientHttpClient/"]
COPY ["FA_Libraries/Libraries.CommonModels/Libraries.CommonModels.csproj", "FA_Libraries/Libraries.CommonModels/"]
COPY ["PerfectStore.DbStorage/PerfectStore.DbStorage.csproj", "PerfectStore.DbStorage/"]
COPY ["FA_Libraries/Library.SqlHelper/Library.SqlHelper.csproj", "FA_Libraries/Library.SqlHelper/"]
RUN dotnet restore "./PerfectStoreProcessor/PerfectStoreProcessor.csproj"
COPY . .
WORKDIR "/src/PerfectStoreProcessor"
RUN dotnet build "./PerfectStoreProcessor.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./PerfectStoreProcessor.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "PerfectStoreProcessor.dll"]