﻿

namespace PerfectStore.Core.Models.DbModels
{
    public class PerfectStoreRule
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public string? Name { get; set; }

        public string? DisplayName { get; set; }

        public string? Description { get; set; }

        public string? ERPId { get; set; }

        public string? Visibility { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime? EffectiveEndDate { get; set; }

        public bool IsRepeatable { get; set; }

        public int RepeatFrequencyInDays { get; set; }

        public DateTime CreatedAt { get; set; }

        public string? CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public bool IsDeactive { get; set; }

        public bool Deleted { get; set; }
    }
}
