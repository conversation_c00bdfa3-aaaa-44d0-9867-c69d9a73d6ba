﻿using Libraries.CommonEnums;
using PerfectStore.Core.Models.DbModels;

namespace PerfectStore.Core.Repositories.ReportRepositories;

public interface IReportKpiRepostory
{
    Task<string> GetKPIAchievedTarget(
        string query,
        long companyId,
        long entityId,
        FilterConstraintEntityType entityType,
        long? productDivisionId,
        DateTime startDate,
        DateTime endDate,
        int monthNumber
    );

    Task<string?> GetReportMetricValue(
        string query,
        long companyId,
        long entityId,
        FilterConstraintEntityType entityType,
        long? productDivisionId,
        DateTime startDate,
        DateTime endDate,
        int monthNumber,
        string? parameterValues = null
    );
}
