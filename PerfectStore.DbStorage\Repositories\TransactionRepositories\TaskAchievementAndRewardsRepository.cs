﻿using Library.DateTimeHelpers;
using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels.TransactionDbModels;
using PerfectStore.Core.Repositories.TransactionRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.TransactionRepositories
{
    public class TaskAchievementAndRewardsRepository(TransactionDbContext _transactionDbContext)
        : ITaskAchievementAndRewardsRepository
    {
        private readonly TransactionDbContext _transactionDbContext = _transactionDbContext;
        public async Task<List<TaskAchievementAndReward>> GetTaskAchievementAndReward(
            long companyId,
            long entityId,
            long focusAreaId
        )
        {
            return await _transactionDbContext
                .TaskAchievementAndRewards.Where(s =>
                    s.CompanyId == companyId
                    && s.TaskEntityId == entityId
                    && s.FocusAreaId == focusAreaId
                )
                .ToListAsync();
        }

        public async Task<TaskAchievementAndReward> GetRecentTaskAchievementAndReward(
            long companyId,
            long entityId,
            long focusAreaId
        )
        {
            return await _transactionDbContext
                .TaskAchievementAndRewards.Where(s =>
                    s.CompanyId == companyId
                    && s.TaskEntityId == entityId
                    && s.FocusAreaId == focusAreaId
                )
                .OrderByDescending(s => s.Id)
                .FirstOrDefaultAsync();
        }

        public async Task<List<TaskAchievementAndReward>> GetTaskAcheivementAndRewards(
            long companyId,
            DateTime startDate,
            DateTime endDate
        )
        {
            return await _transactionDbContext
                .TaskAchievementAndRewards.Where(s =>
                    s.CompanyId == companyId
                    && s.DateKey >= startDate.GetDateKey()
                    && s.DateKey <= endDate.GetDateKey()
                )
                .ToListAsync();
        }
    }
}
