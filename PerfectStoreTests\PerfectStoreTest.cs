﻿using Microsoft.Extensions.DependencyInjection;
using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Services;
using PerfectStoreProcessor.Configurations;

// This is not for mocking tests, but for actual testing of the PerfectStore

// namespace PerfectStore.Tests
// {
//     [TestClass]
//     public class PerfectStoreTest
//     {
//         private ServiceProvider _serviceProvider;

//         [TestInitialize]
//         public void Initialize()
//         {
//             //Environment.SetEnvironmentVariable("BuildEnvironment", "ManageTesting");
//             Environment.SetEnvironmentVariable(
//                 "KEYVAULT_ENDPOINT",
//                 "https://v3ManageWritable.vault.azure.net/"
//             );
//             //Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3DebugWritable.vault.azure.net/");
//             var configuration = Configuration.GetConfiguration();

//             IServiceCollection serviceCollection = new ServiceCollection();
//             PerfectStoreProcessor.Configurations.Dependencies.SetUp(
//                 configuration,
//                 serviceCollection
//             );
//             _serviceProvider = serviceCollection.BuildServiceProvider();
//         }

//         [TestMethod]
//         public async Task ProcessPerfectStoreTask()
//         {
//             var data = new PerfectStoreTaskQueueData { CompanyId = 203267, RuleId = 6 };
//             await _serviceProvider
//                 .GetRequiredService<IPerfectStoreTaskService>()
//                 .ProcessQueue(data);
//         }

//         [TestMethod]
//         public async Task ProcessPerfectStoreTag()
//         {
//             var data = new PerfectStoreTagQueueData
//             {
//                 CompanyId = 203267,
//                 RuleId = 6,
//                 IsRepeatable = true,
//                 RepeatFrequency = 30,
//                 StartDate = new DateTime(2024, 07, 01),
//                 EffectiveEndDate = new DateTime(2024, 07, 31),
//                 CurrentDate = DateTime.Now,
//                 FocusAreaIds = new List<long> { 20047, 20048, 20050 },
//             };
//             await _serviceProvider.GetRequiredService<IPerfectStoreTagService>().ProcessQueue(data);
//         }

//         [TestMethod]
//         public async Task ProcessFocusArea()
//         {
//             var data = new FocusAreaQueueData
//             {
//                 CompanyId = 203267,
//                 RuleId = 6,
//                 IsRepeatable = true,
//                 RepeatFrequency = 30,
//                 StartDate = new DateTime(2024, 08, 01),
//                 EffectiveEndDate = new DateTime(2024, 08, 31),
//                 CurrentDate = DateTime.Now,
//             };
//             await _serviceProvider.GetRequiredService<IFocusAreaService>().ProcessQueue(data);
//         }
//     }
// }