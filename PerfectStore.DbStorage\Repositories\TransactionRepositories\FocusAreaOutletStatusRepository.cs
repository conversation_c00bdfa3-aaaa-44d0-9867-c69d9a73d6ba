﻿using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Repositories.TransactionRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.TransactionRepositories
{
    public class FocusAreaOutletStatusRepository(
        WritableTransactionDbContext writableTransactionDb,
        TransactionDbContext transactionDbContext
    ) : IFocusAreaOutletStatusRepository
    {
        public async Task UpsertFocusAreaOutletStatus(
            List<FocusAreaOutletStatus> newRecords,
            long companyId,
            DateTime startDate,
            DateTime endDate
        )
        {
            var focusAreaIds = newRecords.Select(s => s.FocusAreaId).Distinct().ToList();
            var existingRecords = await writableTransactionDb
                .FocusAreaOutletStatuses.Where(f =>
                    f.CompanyId == companyId
                    && f.StartDate >= startDate
                    && f.EndDate <= endDate
                    && focusAreaIds.Contains(f.FocusAreaId)
                )
                .ToListAsync();
            var newRecordToInsert = new List<FocusAreaOutletStatus>();
            if (existingRecords?.Count > 0)
            {
                var existingRecordsDict = existingRecords
                    .GroupBy(s => new { s.OutletId, s.FocusAreaId })
                    .ToDictionary(gr => gr.Key, gr => gr.FirstOrDefault());
                // Update
                foreach (var newRecord in newRecords)
                {
                    var key = new { newRecord.OutletId, newRecord.FocusAreaId };
                    var existingRecord = existingRecordsDict.GetValueOrDefault(key);
                    if (existingRecord != null)
                    {
                        existingRecord.IsCompleted = newRecord.IsCompleted;
                        existingRecord.CompletionRate = newRecord.CompletionRate;
                        existingRecord.LastUpdatedAt = DateTime.Now;
                        existingRecord.CreationContext = newRecord.CreationContext;
                    }
                    else
                    {
                        // key of new record does not exist in existing records
                        // insert the new record
                        var data = new FocusAreaOutletStatus
                        {
                            OutletId = newRecord.OutletId,
                            FocusAreaId = newRecord.FocusAreaId,
                            CompanyId = companyId,
                            StartDate = startDate,
                            EndDate = endDate,
                            IsCompleted = newRecord.IsCompleted,
                            CompletionRate = newRecord.CompletionRate,
                            LastUpdatedAt = DateTime.Now,
                            CreationContext = newRecord.CreationContext,
                            CreatedAt = DateTime.UtcNow,
                        };
                        newRecordToInsert.Add(data);
                    }
                }
                if (newRecordToInsert.Count > 0)
                {
                    await writableTransactionDb.FocusAreaOutletStatuses.AddRangeAsync(
                        newRecordToInsert
                    );
                }
            }
            else
            {
                // Insert
                await writableTransactionDb.FocusAreaOutletStatuses.AddRangeAsync(newRecords);
            }

            await writableTransactionDb.SaveChangesAsync();
        }

        public async Task<List<FocusAreaOutletStatus>> GetFocusAreaOutletStatus(
            long companyId,
            List<long> focusAreaIds,
            DateTime startDate,
            DateTime endDate
        )
        {
            return await transactionDbContext
                .FocusAreaOutletStatuses.Where(f =>
                    f.CompanyId == companyId
                    && f.StartDate >= startDate
                    && f.EndDate <= endDate
                    && focusAreaIds.Contains(f.FocusAreaId)
                )
                .ToListAsync();
        }
    }
}
