﻿using Libraries.CommonEnums;

namespace PerfectStore.Core.Models.DbModels
{
    public class TaskManagementFocusArea
    {
        public long Id { get; set; }

        public string? Name { get; set; }

        public TaskManagementFocusAreaType? Type { get; set; }

        public long CompanyId { get; set; }

        public long? EntityId { get; set; }

        public bool? IsAutoSequenceRequired { get; set; }

        public SequencingType? SequencingType { get; set; }

        public bool? IsDeactive { get; set; }

        public DateTime CreatedAt { get; set; }

        public string? CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public long ParentId { get; set; }

        public ParentType ParentType { get; set; }

        public ProcessingType ProcessingType { get; set; }

        public ProductLevelHierarchy? TaskLevelHierarchy { get; set; }

        public SalesType? SaleType { get; set; }

        public TagLevel? TagLevel { get; set; }

        public TaskManagementUserFocusArea TaskManagementUserFocusAreas { get; set; }

        public ICollection<TaskManagementTask> TaskManagementTasks { get; set; }
    }

    public enum TagLevel
    {
        All = 0,
        TopBasedOnDistributorStock = 1
    }
}
