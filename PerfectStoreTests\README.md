# PerfectStore Test Suite

This directory contains tests for the PerfectStore project, including both unit tests and integration tests. The test suite uses xUnit and FluentAssertions to verify the functionality of components both in isolation and working together.

## Test Structure

The tests are organized into the following categories:

1. **Integration Tests** - Tests that verify multiple components working together
   - `Integration/PerfectEntityContinuousIntegrationTests.cs` - Tests for PerfectEntityContinuous processor using mock implementations
   - `PerfectEntityTriggeredProcessorTest.cs` - Tests for the PerfectEntityTriggered processor

2. **Unit Tests** - Tests that verify individual components in isolation
   - `PerfectEntityContinuousProcessorTests.cs` - Tests for the PerfectEntityContinuousProcessor using mocks

3. **Test Helpers** - Utilities to assist with testing
   - `TestDataGenerator.cs` - Generates test data for use in tests

## Test Framework

The test suite uses the following frameworks and libraries:

- **xUnit** - Modern testing framework for .NET
- **FluentAssertions** - More readable and expressive assertions
- **NSubstitute** - Mocking framework for creating test doubles

## Running the Tests

To run the tests, you can use the Visual Studio Test Explorer or the .NET CLI:

```bash
# Run all tests
dotnet test PerfectStoreTests

# Run a specific test class
dotnet test --filter "FullyQualifiedName~PerfectStore.Tests.Integration.PerfectEntityContinuousIntegrationTests"

# Run a specific test method
dotnet test --filter "FullyQualifiedName~PerfectStore.Tests.Integration.PerfectEntityContinuousIntegrationTests.ProcessQueueAsync_WithValidData_ProcessesSuccessfully"
```

## Testing Approaches

### Integration Testing with Mock Implementations

Some integration tests use mock implementations of interfaces to test components working together without requiring actual database connections. This approach allows for faster, more reliable tests that focus on the interaction between components.

#### Example of Mock Repository Implementation

```csharp
// Mock implementation of IPerfectEntityRuleAnalyticClickhouseRepository
private class MockAnalyticClickhouseRepository : IPerfectEntityRuleAnalyticClickhouseRepository
{
    private readonly SavedAnalyticsStorage _storage;

    public MockAnalyticClickhouseRepository(SavedAnalyticsStorage storage)
    {
        _storage = storage;
    }

    public Task SavePerfectEntityRuleAnalyticsAsync(List<PerfectEntityRuleAnalytic> analytics)
    {
        // Store the analytics directly without conversion
        _storage.Analytics.AddRange(analytics);
        return Task.CompletedTask;
    }
}
```

### Integration Testing with In-Memory Databases

Some integration tests use Entity Framework Core's in-memory database provider to test components working together without requiring a real database connection. This allows us to test the interactions between services and repositories in a controlled environment.

#### Example of In-Memory Database Setup

```csharp
// Setup in-memory database contexts with unique names to avoid conflicts
var dbNameSuffix = Guid.NewGuid().ToString();
services.AddDbContext<MasterDbContext>(options =>
    options.UseInMemoryDatabase($"MasterTestDb_{dbNameSuffix}"));
```

### Integration Testing with Actual Databases

Some integration tests connect to actual databases to verify that components work correctly with real database systems. These tests are slower but provide higher confidence in the system's behavior in production environments.

#### Example of Testing Service with Repository

```csharp
// Arrange
var taskService = _serviceProvider.GetRequiredService<IPerfectStoreTaskService>();
var queueData = TestDataGenerator.CreatePerfectStoreTaskQueueData(companyId: 10580, ruleId: 1);

// Act
await taskService.ProcessQueue(queueData);

// Assert
var taskManagementRepository = _serviceProvider.GetRequiredService<ITaskManagementRepository>();
var focusAreas = await taskManagementRepository.GetTaskManagementFocusAreasForProductTag(queueData.CompanyId, queueData.RuleId);
```

## Test Coverage

### PerfectEntityContinuousIntegrationTests

The `PerfectEntityContinuousIntegrationTests` class contains the following test methods:

1. **ProcessQueueAsync_WithValidData_SavesAnalytics**
   - Tests that the service correctly processes valid queue data
   - Verifies that analytics are saved with the correct company and rule IDs

2. **ProcessQueueAsync_WithNonExistentRuleId_ThrowsException**
   - Tests that the service throws an exception when a non-existent rule ID is provided
   - Verifies that no analytics are saved when an exception is thrown

3. **ProcessQueueAsync_WithMultipleCriterias_SavesAnalyticsForEachCriteria**
   - Tests that the service processes all criteria for a rule
   - Verifies that analytics are saved for each criteria

4. **ProcessQueueAsync_WithZeroCompanyId_ThrowsArgumentException**
   - Tests that the service validates the CompanyId parameter
   - Verifies that an ArgumentException is thrown when CompanyId is zero

5. **ProcessQueueAsync_WithZeroCompanyId_DoesNotSaveAnalytics**
   - Tests that no analytics are saved when CompanyId is invalid

6. **ProcessQueueAsync_WithZeroEntityId_ThrowsArgumentException**
   - Tests that the service validates the EntityId parameter
   - Verifies that an ArgumentException is thrown when EntityId is zero

7. **ProcessQueueAsync_WithZeroEntityId_DoesNotSaveAnalytics**
   - Tests that no analytics are saved when EntityId is invalid

8. **ProcessQueueAsync_WithDistributorEntityType_SavesAnalyticsWithCorrectEntityType**
   - Tests that the service correctly preserves the entity type in analytics

9. **ProcessQueueAsync_WithDistributorEntityType_UsesSurveyCriteriaType**
   - Tests that the service uses the correct criteria type for Distributor entity type

10. **ProcessQueueAsync_WithProductDivisionId_SavesAnalyticsWithProductDivisionId**
    - Tests that the service correctly includes the ProductDivisionId in analytics

11. **ProcessQueueAsync_WithProductDivisionId_UsesProductRecommendationCriteriaType**
    - Tests that the service uses the correct criteria type for product division rules

12. **ProcessQueueAsync_WithMultipleCriteriaTypes_SavesAnalyticsForMultipleTypes**
    - Tests that the service processes multiple criteria types in a single rule

13. **ProcessQueueAsync_WithAssetAuditCriteriaType_SavesAssetAuditAnalytics**
    - Tests that the service correctly processes the AssetAudit criteria type

14. **ProcessQueueAsync_WithPerfectCallCriteriaType_SavesPerfectCallAnalytics**
    - Tests that the service correctly processes the PerfectCall criteria type

15. **ProcessQueueAsync_WithHistoricalDate_SavesAnalyticsWithCorrectDateKey**
    - Tests that the service correctly calculates the DateKey for historical dates

## Best Practices

Our tests follow Microsoft's [unit testing best practices](https://learn.microsoft.com/en-us/dotnet/core/testing/unit-testing-best-practices):

1. **Naming Convention** - Test names follow the pattern `UnitOfWork_StateUnderTest_ExpectedBehavior`
   - Example: `ProcessQueueAsync_WithZeroCompanyId_ThrowsArgumentException`

2. **Arrange-Act-Assert Pattern** - All tests are structured in three distinct sections:
   - **Arrange**: Set up the test data and conditions
   - **Act**: Perform the action being tested
   - **Assert**: Verify the expected outcome

3. **One Assert Per Test Method** - Each test verifies just one behavior
   - Tests that previously had multiple assertions have been split into separate tests
   - Example: Testing entity type and criteria type in separate test methods

4. **Avoid Logic in Tests** - Tests are kept simple and straightforward
   - Complex setup is handled by helper methods and classes
   - Test data is generated using the `TestDataGenerator` helper class

5. **XML Documentation** - All test methods include XML documentation that explains:
   - What is being tested
   - The expected behavior
   - Any special conditions or edge cases

6. **Isolated Tests** - Each test is isolated and does not depend on the state of other tests
   - Tests use fresh data for each run
   - Mock implementations are reset between tests

7. **Multiple Testing Approaches** - Different testing approaches are used based on the requirements
   - Mock implementations for fast, isolated tests
   - In-memory databases for testing repository interactions
   - Actual database connections for high-confidence integration tests

8. **Comprehensive Test Coverage** - Tests cover various scenarios, edge cases, and error conditions
   - Happy path scenarios
   - Error handling and validation
   - Edge cases and special conditions

## Continuous Integration

The project is set up with continuous integration using Azure DevOps:

### Azure DevOps Pipeline

The Azure DevOps pipeline is defined in `azure-develop-pipelines.yml` and runs on every push to the develop branch and on pull requests to develop, main, and release branches. It consists of two stages and runs on the custom `docker-agents` pool.

#### Build and Test Stage

1. **Checkout**: Checkout the code with submodules and credentials
2. **Code Quality**: Run code formatting checks using `dotnet format`
3. **SonarQube Analysis**: Prepare SonarQube analysis for PRs to develop
4. **Build**: Restore packages and build the solution
5. **Test**: Run tests with the simplified test task
6. **SonarQube Publish**: Analyze and publish SonarQube results for PRs to develop
7. **Publish Results**: Publish test results and build artifacts

#### Docker Build and Push Stage

1. **Checkout**: Checkout the code with submodules
2. **Docker Setup**: Check Docker version and set up Docker Buildx
3. **Docker Build and Push**: Build and push the Docker image for PerfectEntityContinuousProcessor
   - The image is tagged with the build number and 'latest'
   - The image is built for the linux/amd64 platform
   - The image is pushed to the container registry

### PR Validation

The pipeline includes special steps for pull request validation:

1. **Code Formatting**: Ensures code follows consistent formatting standards
2. **SonarQube Analysis**: Performs code quality analysis on PRs to develop
3. **Test Execution**: Runs all tests to ensure code changes don't break existing functionality

### Simplified Test Task

The pipeline uses a simplified test task that runs all tests in the solution:

```yaml
- task: DotNetCoreCLI@2
  displayName: 'Test the solution'
  inputs:
    azureSubscription: 'FA_F2K'
    command: 'test'
    projects: '**/*.csproj'
    arguments: '--no-restore'
```
