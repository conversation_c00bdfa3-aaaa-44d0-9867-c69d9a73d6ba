﻿using PerfectStore.Core.Helpers.QueueHandlers;
using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Repositories.MasterRepositories;

namespace PerfectStore.Core.Services
{
    public interface IPerfectStoreContinuousService
    {
        Task Process(PerfectStoreContinuousQueueData data);
    }

    public class PerfectStoreContinousService(
        IPerfectStoreRuleRepository perfectStoreRuleRepository,
        PerfectStoreTaskQueueHandler perfectStoreTaskQueueHandler,
        FocusAreaQueueHandler focusAreaQueueHandler
    ) : IPerfectStoreContinuousService
    {
        public async Task Process(PerfectStoreContinuousQueueData data)
        {
            var companyId = data.CompanyId;
            var dateTime = DateTime.UtcNow;
            // date check should also be there
            var activeRules = await perfectStoreRuleRepository.GetActivePerfectStoreRules(
                companyId,
                dateTime
            );
            if (activeRules?.Count > 0)
            {
                foreach (var rule in activeRules)
                {
                    // product tag queue
                    await perfectStoreTaskQueueHandler.AddToQueue(
                        new PerfectStoreTaskQueueData { RuleId = rule.Id, CompanyId = companyId }
                    );

                    // focus-area queue
                    await focusAreaQueueHandler.AddToQueue(
                        new FocusAreaQueueData
                        {
                            RuleId = rule.Id,
                            CompanyId = companyId,
                            IsRepeatable = rule.IsRepeatable,
                            RepeatFrequency = rule.RepeatFrequencyInDays,
                            StartDate = rule.StartDate,
                            EffectiveEndDate = rule.EffectiveEndDate,
                            CurrentDate = dateTime,
                        }
                    );
                }
            }
        }
    }
}
