{"Logging": {"IncludeScopes": false, "Debug": {"LogLevel": {"Default": "Warning"}}, "Console": {"LogLevel": {"Default": "Warning"}}}, "AppSettings": {"Storage": "apiblob", "Deployment": "dev", "NS_DataApiHost": "https://fasolutionv3-reporting-debug.azurewebsites.net/", "DataAPIToken": "56SbkbmV+#?p+dSNgGPNz8"}, "TargetApiSettings": {"TargetApiUrl": "https://fa-target-achievement-api.fieldassist.io/", "TargetAchApiToken": "Vz$3bry2B&mjERFcy6v&bwe^R&#FL4nA"}, "ConnectionStrings": {"MasterStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=fadebug;AccountKey=DefaultEndpointsProtocol=https;AccountName=fadebug;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;EndpointSuffix=core.windows.net", "StorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=faappapiv3debug;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "TransactionDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=FA_Transactions;user id=readonlylogin;password=readtoday1@April;", "WritableTransactionDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=FA_Transactions;user id=readonlylogin;password=readtoday1@April;", "WritableReportDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=FA_Reports;user id=readonlylogin;password=readtoday1@April;", "MasterDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=Test_F2KLocationsNetwork;user id=readonlylogin;password=readtoday1@April;", "RedisCacheConnectionString": "fieldassist.redis.cache.windows.net:6380,password=MMOK4o+URrY+QDG00KFEJyzH9FwxlcOuQiin0UQSwOw=,ssl=True,abortConnect=False", "AzureWebJobsDashboard": "DefaultEndpointsProtocol=https;AccountName=faappapiv3debug;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=faappapiv3debug;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "NSDataApiConnectionString": "BaseUrl=https://fasolutionv3-reporting-debug.azurewebsites.net/;AuthToken=56SbkbmV+#?p+dSNgGPNz8;", "FAReportPerspectiveConnectionString": "AccountEndpoint=https://fa-report-perspectives.documents.azure.com:443/;AccountKey=****************************************************************************************;"}}