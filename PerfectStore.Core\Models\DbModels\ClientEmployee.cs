﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PerfectStore.Core.Models.DbModels;

public class ClientEmployee
{
    public long? AreaSalesManagerId { get; set; }

    public string AuthKey { get; set; }

    public long? CompanyZoneId { get; set; }

    [StringLength(100)]
    public string Zone { get; set; }

    public DateTime? DateOfJoining { get; set; }

    public double? Salary { get; set; }

    public long? DesignationId { get; set; }

    public string EmailId { get; set; }

    public bool IsFieldAppuser { get; set; }

    public bool IsOrderBookingDisabled { get; set; }

    public bool IsTrainingUser { get; set; }

    public bool IsVacant { get; set; }

    public long? KRATagId { get; set; }

    public string LocalName { get; set; }

    public Guid? LoginGuid { get; set; }

    public long? OldTableId { get; set; }

    public ClientEmployee Parent { get; set; }

    public long? ParentId { get; set; }

    public string PhoneNo { get; set; }

    public EmployeeRank Rank { get; set; }

    public long? RegionId { get; set; }

    public EmployeeType UserType { set; get; }

    [StringLength(50)]
    public string EmployeeAttributeText1 { get; set; }

    [StringLength(50)]
    public string EmployeeAttributeText2 { get; set; }

    public bool? EmployeeAttributeBoolean1 { get; set; }
    public bool? EmployeeAttributeBoolean2 { get; set; }
    public double? EmployeeAttributeNumber1 { get; set; }
    public double? EmployeeAttributeNumber2 { get; set; }
    public DateTime? EmployeeAttributeDate { get; set; }
    public virtual ICollection<PositionCodeEntityMapping> PositionCodeEntityMappings { set; get; }

    public DateTime CreatedAt { get; set; }
    public string CreationContext { get; set; }

    public long Id { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public long CompanyId { get; set; }

    public Guid Guid { get; set; }

    public bool IsBillable { set; get; }

    public string Name { get; set; }

    public PortalUserRole UserRole { get; set; }

    [Column("Deleted")]
    public bool IsDeactive { get; set; }

    [Column("IsDeleted")]
    public bool Deleted { get; set; }

    [Column("ClientSideId")]
    public string ErpId { get; set; }

    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
}

[Table("PositionCodes")]
public class PositionCode
{
    public PositionCode()
    {
        PositionCodeEntityMappings = new HashSet<PositionCodeEntityMapping>();
    }
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public string CodeId { get; set; }
    public string Name { get; set; }
    public PositionCodeLevel Level { get; set; }
    public long? ParentId { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? CreationContext { get; set; }
    public DateTime LastUpdatedAt { get; set; }
    public bool Deleted { get; set; }
    public PositionCode Parent { get; set; }
    public ICollection<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }
    public ICollection<PositionBeatMapping> PositionBeatMappings { get; set; }
    public ICollection<PositionDistributorMapping> PositionDistributorMappings { get; set; }
}

[Table("PositionBeatMapping")]
public class PositionBeatMapping
{
    public long Id { get; set; }
    [ForeignKey("PositionCode")]
    public long PositionId { get; set; }
    [ForeignKey("LocationBeat")]
    public long BeatId { get; set; }
    [Column("TimeAdded", TypeName = "datetime2")]
    public DateTime CreatedAt { get; set; }
    public long CompanyId { get; set; }
    public string? CreationContext { get; set; }
    public bool IsDeleted { get; set; }
    public virtual PositionCode PositionCode { get; set; }
    public virtual LocationBeat LocationBeat { get; set; }
    public long? ProductDivisionId { get; set; }
}

public class PositionCodeEntityMapping
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public long PositionCodeId { get; set; }
    public long EntityId { get; set; }

    [Column("IsDetached")]
    public bool IsDeactive { get; set; }

    public DateTime CreatedAt { get; set; }
    public string? CreationContext { get; set; }
    public PositionCode PositionCode { get; set; }
}
public class PositionDistributorMapping
{
    public long Id { get; set; }
    [ForeignKey("PositionCode")]
    public long PositionId { get; set; }
    public long DistributorId { get; set; }

    [Column("TimeAdded", TypeName = "datetime2")]
    public DateTime CreatedAt { get; set; }
    public DateTime LastUpdatedAt { get; set; }

    [ForeignKey("Company")]
    public long CompanyId { get; set; }
    public string CreationContext { get; set; }
    public bool IsDeleted { get; set; }

    public virtual PositionCode PositionCode { get; set; }
}