﻿using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.MasterRepositories;

public class CompanyTargetRepository(MasterDbContext masterDbContext) : ICompanyTargetRepository
{
    public async Task<TargetAchievementDto> GetCompanyTargetsWithSubscriptions(
        long companyId,
        long outletId,
        long? productDivisionId,
        long companyTargetSubscriptionId,
        DateTime startDate,
        DateTime endDate
    )
    {
        var query =
            from cts in masterDbContext.CompanyTargetSubscriptions
            join ct in masterDbContext.CompanyTargets
                on cts.Id equals ct.CompanyTargetSubscriptionId
            join tm in masterDbContext.TargetMaster
                on cts.TargetMasterId equals tm.Id
            where cts.CompanyId == companyId
                  && cts.Id == companyTargetSubscriptionId
                  && (
                      ct.Hierarchy1Id == outletId ||
                      (ct.Hierarchy2Id ?? 0) == outletId ||
                      (ct.Hierarchy3Id ?? 0) == outletId
                  )
                  && ct.StartDate >= startDate
                  && ct.EndDate <= endDate
            select new
            {
                cts,
                ct,
                tm
            };

        if (productDivisionId.HasValue)
        {
            var pid = productDivisionId.Value;
            query = query.Where(x =>
                x.ct.Hierarchy1Id == pid ||
                (x.ct.Hierarchy2Id ?? 0) == pid ||
                (x.ct.Hierarchy3Id ?? 0) == pid
            );
        }

        return await query
            .Select(x => new TargetAchievementDto
            {
                TargetId = x.cts.Id,
                CompanyTargetId = x.ct.Id,
                TargetMasterId = x.cts.TargetMasterId,
                TargetValue = x.ct.Target,
                TargetStartPeriod = x.ct.StartDate,
                TargetEndPeriod = x.ct.EndDate,
                AchievementQuery = x.tm.AchievementQuery,
                AchievementDb = x.tm.AchievementDb,
                Frequency = x.ct.Frequency,
                Hierarchy1Id = x.ct.Hierarchy1Id,
                Hierarchy2Id = x.ct.Hierarchy2Id,
                Hierarchy3Id = x.ct.Hierarchy3Id,
                Hierarchy1Type = x.tm.Hierarchy1,
                Hierarchy2Type = x.tm.Hierarchy2,
                Hierarchy3Type = x.tm.Hierarchy3
            })
            .ToListAsync();
    }
}
