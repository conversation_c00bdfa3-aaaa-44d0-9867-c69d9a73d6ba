﻿using Library.ConnectionStringParsor;
using Library.Infrastructure.QueueService;
using Library.ResiliencyHelpers;
using Library.SlackService;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PerfectStore.Core.Helpers.QueueHandlers;
using PerfectStore.Core.Models;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.Core.Repositories.TransactionRepositories;
using PerfectStore.Core.Services;
using PerfectStore.Core.Utils.Helpers;
using PerfectStore.DbStorage.DbContexts;
using PerfectStore.DbStorage.Repositories.MasterRepositories;
using PerfectStore.DbStorage.Repositories.TransactionRepositories;

namespace PerfectStoreProcessor.Configurations
{
    public static class Dependencies
    {
        public static void SetUp(IConfiguration config, IServiceCollection services)
        {
            #region Connection-Strings
            var masterDbConnectionString = config.GetConnectionString("MasterDbConnectionString");
            var writableMasterDbConnectionString = config.GetConnectionString(
                "WritableMasterDbConnectionString"
            );
            var writableTransactionDbConnectionString = config.GetConnectionString(
                "WritableTransactionDbConnectionString"
            );
            var transactionDbConnectionString = config.GetConnectionString(
                "TransactionDbConnectionString"
            );
            var nsDataApiConnectionString = config.GetConnectionString("NSDataApiConnectionString");
            var masterStorageConnectionString = config.GetConnectionString(
                "MasterStorageConnectionString"
            );
            var storageConnectionString = config.GetConnectionString("StorageConnectionString");
            #endregion

            #region DbContexts
            //MasterDbContext
            services.AddDbContext<MasterDbContext>(options =>
                options.UseSqlServer(masterDbConnectionString)
            );

            //WritableMasterDbContext
            services.AddDbContext<WritableMasterDbContext>(options =>
                options.UseSqlServer(writableMasterDbConnectionString)
            );

            //WritableTransactionDbContext
            services.AddDbContext<WritableTransactionDbContext>(options =>
                options.UseSqlServer(writableTransactionDbConnectionString)
            );

            //readonly TransactionDbContext
            services.AddDbContext<TransactionDbContext>(options =>
                options.UseSqlServer(transactionDbConnectionString)
            );
            #endregion

            #region repos
            services.AddScoped<ICompanySettingRepository, CompanySettingRepository>();
            services.AddScoped<IPerfectStoreRuleRepository, PerfectStoreRuleRepository>();
            services.AddScoped<ITaskManagementRepository, TaskManagmentRepository>();
            services.AddScoped<IProductTagRepository, ProductTagRepository>();
            services.AddScoped<
                ITaskAchievementAndRewardsRepository,
                TaskAchievementAndRewardsRepository
            >();
            services.AddScoped<IFocusAreaOutletStatusRepository, FocusAreaOutletStatusRepository>();
            services.AddScoped<ITargetModuleRepository, TargetModuleRepository>();
            services.AddScoped<
                IPerfectStoreOutletStatusRepository,
                PerfectStoreOutletStatusRepository
            >();
            #endregion

            #region services
            services.AddScoped<PerfectStoreContinuousProcessor>();
            services.AddScoped<IPerfectStoreContinuousService, PerfectStoreContinousService>();
            services.AddScoped<IPerfectStoreTaskService, PerfectStoreTaskService>();
            services.AddScoped<IPerfectStoreTagService, PerfectStoreTagService>();
            services.AddScoped<IFocusAreaService, FocusAreaService>();
            services.AddScoped<MTDService>();
            services.AddScoped<TargetAchievementService>();
            #endregion

            #region infrastructure
            var reportApiConnection = ApiConnectionString.GetConnection(nsDataApiConnectionString);
            var reportApiBaseUrl = reportApiConnection.BaseUrl;
            var reportApiToken = reportApiConnection.AuthToken;
            var targetApiBaseUrl = config.GetValue<string>("TargetApiSettings:TargetApiUrl");
            var targetApiToken = config.GetValue<string>("TargetApiSettings:TargetAchApiToken");
            services.AddSingleton(s => new AppConfigSettings
            {
                reportApiBaseUrl = reportApiBaseUrl,
                reportApiToken = reportApiToken,
                targetApiBaseUrl = targetApiBaseUrl,
                targetApiToken = targetApiToken,
            });

            services.AddScoped<ResilientAction>();
            services.AddScoped<PerfecStoreTagQueueHandler>(q => new PerfecStoreTagQueueHandler(
                QueueType.PerfectStoreTagQueue,
                storageConnectionString
            ));
            services.AddScoped<FocusAreaQueueHandler>(q => new FocusAreaQueueHandler(
                QueueType.PerfectStoreFocusAreaQueue,
                storageConnectionString
            ));
            services.AddScoped<PerfectStoreTaskQueueHandler>(q => new PerfectStoreTaskQueueHandler(
                QueueType.PerfectStoreTaskQueue,
                storageConnectionString
            ));
            services.AddSingleton(_ => new ErrorMessenger(
                masterStorageConnectionString,
                "perfect-store-error",
                "﻿#perfect-store-error",
                QueueType.SlackCloudLogs
            ));
            #endregion
        }
    }
}
