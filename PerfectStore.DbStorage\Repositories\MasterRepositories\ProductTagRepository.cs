﻿using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.MasterRepositories
{
    public class ProductTagRepository(MasterDbContext masterDbContext) : IProductTagRepository
    {
        public async Task<Dictionary<long, List<ProductSuggestedQtyDto>>> GetProductSuggestedQuantities(
            long productTagId,
            long companyId)
        {
            return await masterDbContext
                .ProductTagSuggestions.Where(s =>
                    s.CompanyId == companyId
                    && s.ProductTagId == productTagId
                    && !s.IsDeactive
                    && !s.IsCompleted
                )
                .GroupBy(gr => gr.OutletId)
                .ToDictionaryAsync(
                    r => r.Key,
                    r =>
                        r.Select(q => new ProductSuggestedQtyDto
                            {
                                ProductHierarchyId = q.ProductId,
                                SuggestedQty = q.SuggestedQuantity,
                            })
                            .ToList()
                );
        }

        public async Task<List<ProductTagSuggestion>> GetProductTagSuggestions(
            long companyId,
            long productTagId
        )
        {
            return await masterDbContext
                .ProductTagSuggestions.Where(s =>
                    s.CompanyId == companyId && s.ProductTagId == productTagId && !s.IsDeactive
                )
                .ToListAsync();
        }

        public async Task<long> GetProductTagSuggestionCompletionCount(
            long companyId,
            long outletId,
            long productTagId
        )
        {
            return await masterDbContext
                .ProductTagSuggestions.Where(s =>
                    s.CompanyId == companyId
                    && s.OutletId == outletId
                    && s.ProductTagId == productTagId
                    && s.IsCompleted
                    && !s.IsDeactive
                )
                .CountAsync();
        }
    }
}
