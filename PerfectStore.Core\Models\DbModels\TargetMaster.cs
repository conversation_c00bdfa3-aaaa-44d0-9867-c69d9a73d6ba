﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PerfectStore.Core.Models.DbModels
{
    [Table("TargetMaster")]
    public class TargetMaster
    {
        public long Id { get; set; }

        public string TargetIdentifier { get; set; }

        public string TargetName { get; set; }

        public FlexibleTargetEntityType Hierarchy1 { get; set; }

        public FlexibleTargetEntityType? Hierarchy2 { get; set; }

        public FlexibleTargetEntityType? Hierarchy3 { get; set; }

        public string TargetOn { get; set; }

        public string Hierarchy1Query { get; set; }

        public string Hierarchy2Query { get; set; }

        public string Hierarchy3Query { get; set; }

        public string AchievementQuery { get; set; }

        public OutletMetricQueryRelation AchievementDb { get; set; }

        [Column("AchievementReturnType")]
        public AchievementReturnType AchievementReturnType { get; set; }

        public int? AppScreen { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public string CreationContext { get; set; }

        public int? VisualizationType { get; set; }

        public string TargetFilters { get; set; }

        public int? DisplayAxis { get; set; }

        public bool? IsForApp { get; set; }
    }
}
