﻿using PerfectStore.Core.Helpers;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.Core.Repositories.TransactionRepositories;
using PerfectStore.Core.Utils.Helpers;

namespace PerfectStore.Core.Services
{
    public interface IPerfectStoreTagService
    {
        Task ProcessQueue(PerfectStoreTagQueueData data);
    }

    public class PerfectStoreTagService(
        IPerfectStoreOutletStatusRepository perfectStoreOutletStatusRepository,
        IFocusAreaOutletStatusRepository focusAreaOutletStatusRepository,
        MTDService mTDService
    ) : IPerfectStoreTagService
    {
        public async Task ProcessQueue(PerfectStoreTagQueueData data)
        {
            var yearStartMonth = data.YearStartMonth;
            var todayDate = data.CurrentDate.Add(data.TimeZoneOffset).Date;
            var mtdLmtd = await mTDService.GetDates(
                data.CompanyId,
                todayDate,
                yearStartMonth,
                true
            );
            var startDate = mtdLmtd.MTD.StartDate;
            var daysInMonth = DateTime.DaysInMonth(todayDate.Year, todayDate.Month);
            var endDate = data.IsRepeatable
                ? (daysInMonth > data.RepeatFrequency)
                    ? startDate.AddDays(data.RepeatFrequency)
                    : startDate.AddDays(data.RepeatFrequency - 1)
                : data.EffectiveEndDate ?? todayDate;

            var focusAreaOutletStatus =
                await focusAreaOutletStatusRepository.GetFocusAreaOutletStatus(
                    data.CompanyId,
                    data.FocusAreaIds,
                    startDate,
                    endDate
                );

            if (focusAreaOutletStatus?.Count > 0)
            {
                var newRecords = CalculatePerfectStoreStatus(
                    focusAreaOutletStatus,
                    data.CompanyId,
                    data.RuleId,
                    startDate,
                    endDate
                );
                if (newRecords?.Count > 0)
                {
                    await perfectStoreOutletStatusRepository.UpsertPerfectStoreOutletStatus(
                        newRecords,
                        data.CompanyId,
                        data.RuleId,
                        startDate,
                        endDate
                    );
                }
            }
        }

        private static List<PerfectStoreOutletStatus> CalculatePerfectStoreStatus(
            List<FocusAreaOutletStatus> focusAreaOutletStatuses,
            long companyId,
            long ruleId,
            DateTime startDate,
            DateTime endDate
        )
        {
            var data = new List<PerfectStoreOutletStatus>();
            var focusAreaOutletWiseDict = focusAreaOutletStatuses
                .GroupBy(s => s.OutletId)
                .ToDictionary(gr => gr.Key, gr => gr.ToList());

            foreach (var outletId in focusAreaOutletWiseDict.Keys.ToList())
            {
                var focusAreaData = focusAreaOutletWiseDict[outletId];
                var completedFocusAreas = focusAreaData.Where(s => s.IsCompleted).ToList();
                var completionRate = Math.Round(
                    (completedFocusAreas.Count / (float)focusAreaData.Count) * 100,
                    2
                );
                if (completionRate > 100)
                {
                    completionRate = 100;
                }
                var perfectStoreOutletStatus = new PerfectStoreOutletStatus
                {
                    CompanyId = companyId,
                    OutletId = outletId,
                    PerfectStoreRuleId = ruleId,
                    CreatedAt = DateTime.UtcNow,
                    CreationContext = "perfect-store-tag-processor",
                    StartDate = startDate,
                    EndDate = endDate,
                    IsCompleted = completedFocusAreas.Count == focusAreaData.Count,
                    CompletionRate = completionRate,
                    GeographicalHierarchy = 0,
                };
                data.Add(perfectStoreOutletStatus);
            }
            return data;
        }
    }
}
