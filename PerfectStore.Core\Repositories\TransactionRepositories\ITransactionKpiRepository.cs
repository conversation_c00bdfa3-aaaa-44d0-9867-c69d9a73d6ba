﻿using PerfectStore.Core.Models.DbModels;

namespace PerfectStore.Core.Repositories.TransactionRepositories;

public interface ITransactionKpiRepository
{
    Task<string?> GetKPIAchievedTarget(
        string query,
        long companyId,
        long entityId,
        FilterConstraintEntityType entityType,
        long? productDivisionId,
        DateTime startDate,
        DateTime endDate,
        int monthnumber
    );

    Task<string?> GetTransactionMetricValue(
        string query,
        long companyId,
        long entityId,
        FilterConstraintEntityType entityType,
        long? productDivisionId,
        DateTime startDate,
        DateTime endDate,
        int monthNumber,
        string? parameterValue = null
    );
}
