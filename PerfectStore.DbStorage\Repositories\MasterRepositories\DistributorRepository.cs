﻿using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Models.DbModels.TransactionDbModels;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.MasterRepositories;

public class DistributorRepository : IDistributorRepository
{
    private readonly MasterDbContext _masterDbContext;
    private readonly TransactionDbContext _transactionDbContext;
    public DistributorRepository(MasterDbContext masterDbContext, TransactionDbContext transactionDbContext)
    {
        _masterDbContext = masterDbContext;
        _transactionDbContext = transactionDbContext;
    }

    public async Task<List<long>> GetRegionIdsForDistributorIds(List<long> distributorIds, long companyId)
    {
        return await _masterDbContext.FADistributors
            .Where(s => s.CompanyId == companyId && distributorIds.Contains(s.Id) && s.RegionId.HasValue)
            .Select(s => s.RegionId!.Value)
            .ToListAsync();
    }

    public async Task<List<long>> GetDistributorIdsForChannelAndSegmentationIds(
        List<long> channelIds, List<long> segmentationIds, long companyId)
    {
        return await _masterDbContext.FADistributors
            .Where(s => s.CompanyId == companyId && s.DistributorSegmentationId.HasValue && s.DistributorChannelId.HasValue &&
                        channelIds.Contains(s.DistributorChannelId.Value) &&
                        segmentationIds.Contains(s.DistributorSegmentationId.Value))
            .Select(s => s.Id)
            .ToListAsync();
    }

    public async Task<List<Distributor>> GetAllDistributorForCompany(long companyId)
    {
        return await _masterDbContext.FADistributors
            .Where(s => s.CompanyId == companyId && !s.Deleted)
            .ToListAsync();
    }

    public async Task<List<(long OutletId, long BeatId, long DistributorId)>> GetBeatAndDistributorFromOutlets(long companyId, List<long> outletIds)
    {
        var query = from outlet in _masterDbContext.Locations
                    join distributor in _masterDbContext.DistributorBeatMappings
                        on outlet.BeatId equals distributor.BeatId
                    where
                        outlet.CompanyId == companyId &&
                        distributor.CompanyId == companyId &&
                        !outlet.IsBlocked &&
                        outletIds.Contains(outlet.Id)
                    select new { outlet.Id, distributor.BeatId, distributor.DistributorId };

        var result = await query.ToListAsync();

        return result.Select(s => (s.Id, s.BeatId, s.DistributorId)).ToList();
    }

    public async Task<List<DistributorStock>> GetDistributorStocks(long companyId, List<long> distributorIds, DateTime date)
    {
        var data = await _transactionDbContext.DistributorStocks.Where(d => d.CompanyId == companyId
                                                                              && distributorIds.Contains(d.DistributorId)
                                                                              && d.IsInvalid.HasValue 
                                                                              && !d.IsInvalid.Value 
                                                                              && d.DeviceTime.Date == date.Date)
                .Include(d => d.StockItems).ToListAsync();

        return data;
    }

    public async Task<List<StockistStock>> GetDistributorStocksFromMaster(long companyId, List<long> distributorIds)
    {
        return await _masterDbContext.StockistStocks
                    .Where(s => s.CompanyId == companyId && distributorIds.Contains(s.DistributorId))
                    .Include(s => s.Items).ToListAsync();
    }

}
