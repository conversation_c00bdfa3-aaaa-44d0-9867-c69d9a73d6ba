﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PerfectStore.Core.Models.DbModels.TransactionDbModels
{
    public class TaskAchievementAndReward
    {
        public long Id { get; set; }
        public long TaskId { get; set; }
        public long TaskEntityId { get; set; }
        public double? TaskTarget { get; set; }
        public double? TaskAchievement { get; set; }
        public long? RewardId { get; set; }
        public int? RewardQuantity { get; set; }
        public bool IsCompleted { get; set; }
        public long FaEventId { get; set; }
        public long CompanyId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime SyncedAt { get; set; }
        public long DateKey { get; set; }
        public long? ParentId { get; set; }
        public int? ParentType { get; set; }
        public double? TaskAchievementValue { get; set; }
        public long? FocusAreaId { get; set; }
    }
}
