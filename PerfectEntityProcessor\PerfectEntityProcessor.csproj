﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AspNetCore.KeyVault" Version="1.0.6" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.1" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Sentry.AspNetCore" Version="5.5.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Lite_Library\Library.ConnectionStringParsor\Library.ConnectionStringParsor.csproj" />
    <ProjectReference Include="..\Lite_Library\Library.Infrastructure\Library.Infrastructure.csproj" />
    <ProjectReference Include="..\PerfectStore.DbStorage\PerfectStore.DbStorage.csproj" />
  </ItemGroup>
</Project>
