﻿
namespace PerfectStore.Core.Models.DbModels
{
    public class PerfectStoreOutletStatus
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public long PerfectStoreRuleId { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public long OutletId { get; set; }

        public bool IsCompleted { get; set; }

        public double CompletionRate { get; set; }

        public string CreationContext { get; set; }

        public DateTime CreatedAt { get; set; }

        public int GeographicalHierarchy { get; set; }
    }
}
