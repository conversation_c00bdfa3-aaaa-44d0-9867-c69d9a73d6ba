# PerfectStore Project

## Overview

PerfectStore is a .NET application for managing store rules, entities, and KPIs.

## Testing Strategy

This project follows a modern testing approach with a focus on integration tests using xUnit and FluentAssertions.

### Key Components of the Testing Strategy

1. **Integration Tests** - Test multiple components working together
2. **In-Memory Database Testing** - Use EF Core's in-memory provider for repository tests
3. **Mock Services** - Use NSubstitute for mocking dependencies
4. **Test Data Generation** - Use helper classes to generate test data
5. **Continuous Integration** - Automatically run tests on code changes with code quality checks

### Test Project Structure

The test project is organized into the following directories:

- **Integration/** - Integration tests using in-memory databases
  - **PerfectEntityContinuousIntegrationTests.cs** - Tests for PerfectEntityContinuous processor using mock implementations
- **PerfectEntityContinuousProcessorTests.cs** - Unit tests for the PerfectEntityContinuous processor
- **PerfectEntityTriggeredProcessorTest.cs** - Tests for the PerfectEntityTriggered processor
- **Helpers/** - Test utilities and helpers

### Running Tests

To run the tests, you can use the Visual Studio Test Explorer or the .NET CLI:

```bash
# Run all tests
dotnet test

# Run a specific test project
dotnet test PerfectStoreTests

# Run specific tests
dotnet test --filter "FullyQualifiedName~PerfectEntityContinuousIntegrationTests"

# Run tests with code coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Continuous Integration

The project is set up with continuous integration using Azure DevOps. The pipeline includes:

1. **Code Formatting Checks** - Ensures code follows consistent formatting standards
2. **SonarQube Analysis** - Performs code quality analysis on PRs to develop
3. **Automated Testing** - Runs all tests with the simplified test task
4. **Docker Image Building** - Creates and pushes Docker images for the continuous processor

See the `azure-develop-pipelines.yml` file for details.

## Getting Started

1. Clone the repository
2. Open the solution in Visual Studio
3. Build the solution
4. Run the tests using the Test Explorer

## Documentation

For more detailed information about the test suite, see the [PerfectStoreTests/README.md](PerfectStoreTests/README.md) file.
