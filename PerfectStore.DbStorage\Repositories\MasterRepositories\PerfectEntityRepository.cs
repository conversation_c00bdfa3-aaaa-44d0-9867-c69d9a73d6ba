﻿using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.MasterRepositories;

public class PerfectEntityRepository(MasterDbContext masterDbContext) : IPerfectEntityRepository
{
    public async Task<List<PerfectEntityRule>> GetActivePerfectEntityRules(
        long companyId,
        DateTime date
    )
    {
        return await masterDbContext
            .PerfectEntityRules
            .Include(r => r.FilterConstraintDetails)
            .Include(c => c.Criterias).Where(s =>
                s.CompanyId == companyId
                && !s.IsDeactive
                && !s.Deleted
                && s.StartDate <= date.Date
                && s.EndDate >= date.Date
            )
            .ToListAsync();
    }

    public async Task<FilterConstraintDetail?> GetFilterConstraintDetail(
        long companyId,
        long filterConstraintId
    )
    {
        return await masterDbContext
            .FilterConstraintDetails.Where(s =>
                s.CompanyId == companyId && s.Id == filterConstraintId
            )
            .FirstOrDefaultAsync();
    }

    public async Task<PerfectEntityRule?> GetPerfectEntityRule(long companyId, long ruleId)
    {
        return await masterDbContext
            .PerfectEntityRules.Where(s => s.CompanyId == companyId && s.Id == ruleId)
            .FirstOrDefaultAsync();
    }

    public async Task<PerfectEntityRuleCriteria?> GetPerfectEntityCriteriaByCriteriaId(
        long companyId,
        long criteriaId
    )
    {
        return await masterDbContext
            .PerfectEntityRuleCriterias
            .Include(s => s.SlabDetails).Where(s =>
                s.CompanyId == companyId && s.Id == criteriaId && !s.IsDeactive && !s.Deleted
            )
            .FirstOrDefaultAsync();
    }

    public async Task<List<PerfectEntityRuleCriteria>> GetCriteriasForRule(
        long companyId,
        long ruleId
    )
    {
        return await masterDbContext
            .PerfectEntityRuleCriterias.Where(s => s.CompanyId == companyId && s.RuleId == ruleId && !s.IsDeactive)
            .ToListAsync();
    }

    public async Task<List<PerfectCriteriaSlabDetail>> GetSlabsForCriteria(
        long companyId,
        long criteriaId
    )
    {
        return await masterDbContext
            .PerfectCriteriaSlabDetails.Where(s =>
                s.CompanyId == companyId && s.CriteriaId == criteriaId
            )
            .OrderBy(s => s.Sequence)
            .ToListAsync();
    }

    public async Task<List<PerfectEntityQualifier>> GetPerfectEntityQualifiers(
        long companyId,
        List<long> qualifierIds
    )
    {
            return await masterDbContext
                .PerfectEntityQualifiers.Where(s =>
                    s.CompanyId == companyId && qualifierIds.Contains(s.Id)
                )
                .ToListAsync();
    }

    public async Task<PerfectEntityRule?> GetPerfectEntityRulesWithAll(long companyId, long ruleId)
    {
        var ruleWithAll = await masterDbContext.PerfectEntityRules
                    .Include(r => r.Criterias)
                        .ThenInclude(c => c.SlabDetails)
                    .SingleOrDefaultAsync(r => r.CompanyId == companyId && r.Id == ruleId);

        return ruleWithAll;

    }
}
