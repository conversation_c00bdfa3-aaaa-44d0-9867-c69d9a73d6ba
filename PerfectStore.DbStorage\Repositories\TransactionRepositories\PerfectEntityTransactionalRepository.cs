﻿using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Models.DbModels.TransactionDbModels;
using PerfectStore.Core.Repositories.TransactionRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.TransactionRepositories;

public class PerfectEntityTransactionalRepository(TransactionDbContext transactionDbContext) : IPerfectEntityTransactionalRepository
{
    public async Task<PerfectEntityCallDetail> GetPerfectEntityCallDetails(
        long companyId,
        long criteriaId,
        long ruleId,
        long dateKey,
        long entityId,
        FilterConstraintEntityType entityType
    )
    {
        return await transactionDbContext
            .PerfectEntityCallDetails.Where(s =>
                s.CompanyId == companyId
                && s.CriteriaId == criteriaId
                && s.PerfectEntityRuleId == ruleId
                && s.EntityType == entityType
                && s.DateKey == dateKey
                && s.EntityId == entityId
            )
            .ToListAsync();
    }

    public async Task<List<PerfectEntityCallDetail>> GetPerfectEntityCallDetailsForDateRange(
        long companyId,
        long criteriaId,
        TaskManagementFocusAreaType criteriaType,
        long ruleId,
        long startDateKey,
        long endDateKey,
        long entityId,
        FilterConstraintEntityType entityType
    )
    {
        return await transactionDbContext
            .PerfectEntityCallDetails.Where(s =>
                s.CompanyId == companyId
                && s.CriteriaId == criteriaId
                && s.CriteriaType == criteriaType
                && s.PerfectEntityRuleId == ruleId
                && s.EntityId == entityId
                && s.EntityType == entityType
                && s.DateKey >= startDateKey
                && s.DateKey <= endDateKey
            )
            .ToListAsync();
    }
}
