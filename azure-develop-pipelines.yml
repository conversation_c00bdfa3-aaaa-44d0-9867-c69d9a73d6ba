trigger:
  branches:
    include:
      - refs/heads/develop
  paths:
    exclude:
      - README.md
      - docs/*

variables:
  solution: "**/*.sln"
  buildPlatform: "Any CPU"
  buildConfiguration: "Release"
  environment: "develop"

stages:
  - stage: Build
    displayName: "Build and Test"
    jobs:
      - job: BuildAndTest
        displayName: "Build and Test"
        timeoutInMinutes: 60
        pool:
          name: scaleset-agent
        steps:
          - checkout: self
            clean: true
            submodules: recursive
            fetchDepth: 0

          - task: SonarQubePrepare@7
            condition: eq(variables['Build.SourceBranch'], 'refs/heads/main')
            inputs:
              SonarQube: "SonarQube"
              scannerMode: "dotnet"
              projectKey: "FAi_PerfectStore_aa58383a-0b9b-45ce-a545-ef2e043639c6"
              projectName: "PerfectStore"
              extraProperties: |
                sonar.scanner.skipJreProvisioning=true
                sonar.scanner.scanAll=false
            displayName: "Prepare SonarQube Analysis (Main Branch Only)"

          - task: DotNetCoreCLI@2
            displayName: "Restore Dependencies"
            inputs:
              command: "restore"
              projects: "$(solution)"
              arguments: "--configuration $(buildConfiguration)"

          - task: DotNetCoreCLI@2
            displayName: "Build Solution"
            inputs:
              command: "build"
              projects: "$(solution)"
              arguments: "--configuration $(buildConfiguration) --no-restore" # Skip restore since it's handled separately

          - task: DotNetCoreCLI@2
            condition: eq(variables['Build.SourceBranch'], 'refs/heads/main')
            displayName: "SonarQube Build Step"
            inputs:
              command: "build"
              projects: "$(solution)"
              arguments: "--configuration $(buildConfiguration) --no-restore"

          - task: DotNetCoreCLI@2
            displayName: "Run Tests"
            inputs:
              command: "test"
              projects: "**/*.csproj"
              arguments: "--configuration $(buildConfiguration) --no-build"

          - task: SonarQubeAnalyze@7
            condition: eq(variables['Build.SourceBranch'], 'refs/heads/main')
            inputs:
              jdkversion: "JAVA_HOME"
            displayName: "Run SonarQube Analysis (Main Branch Only)"
          - task: PublishTestResults@2
            displayName: "Publish Test Results"
            inputs:
              testResultsFormat: "VSTest"
              testResultsFiles: "**/*.trx"
              testRunTitle: "Solution Tests"

          - task: SonarQubePublish@7
            condition: eq(variables['Build.SourceBranch'], 'refs/heads/main')
            inputs:
              pollingTimeoutSec: "300"
            displayName: "Publish SonarQube Results (Main Branch Only)"

  - stage: Docker
    displayName: "Docker Build and Push"
    dependsOn: Build
    condition: eq(variables['Build.SourceBranch'], 'refs/heads/develop')
    jobs:
      - job: DockerBuildPush
        displayName: "Docker Build and Push"
        pool:
          name: docker-agents
        steps:
          - checkout: self
            clean: true
            submodules: recursive
            fetchDepth: 0

          - task: Docker@2
            displayName: "Build and Push PerfectEntityContinuous Image"
            inputs:
              containerRegistry: "608d46b7-4971-4e1c-8f07-6e93191fd37f"
              repository: "perfectentitycontinuous-$(environment)"
              command: buildAndPush
              Dockerfile: "PerfectEntityContinuousProcessor/Dockerfile"
              buildContext: "$(Build.SourcesDirectory)"
              tags: |
                $(Build.BuildNumber)
                latest
              arguments: "--platform linux/amd64 --no-cache"

          - task: Docker@2
            displayName: "Build and Push PerfectEntityTriggered Image"
            inputs:
              containerRegistry: "608d46b7-4971-4e1c-8f07-6e93191fd37f"
              repository: "perfect-entity-triggered-$(environment)"
              command: buildAndPush
              Dockerfile: "PerfectEntityProcessor/Dockerfile"
              buildContext: "$(Build.SourcesDirectory)"
              tags: |
                $(Build.BuildNumber)
                latest
              arguments: "--platform linux/amd64 --no-cache"
