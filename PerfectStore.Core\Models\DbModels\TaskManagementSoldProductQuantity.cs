﻿namespace PerfectStore.Core.Models.DbModels;

public class TaskManagementSoldProductQuantity
{
    public long Id { get; set; }

    public long TaskManagementTaskId { get; set; }

    public long TaskLevelHieararchyId { get; set; }

    public long ProductLevelHieararchyId { get; set; }

    public double BilledQty { get; set; }

    public double RemainingBilledQty { get; set; }

    public bool ShowTagInApp { get; set; }

    public double DistributorStock { get; set; }

    public long CompanyId { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public DateTime CreatedAt { get; set; }

    public string? CreationContext { get; set; }

    public int? MOQ { get; set; }
}
