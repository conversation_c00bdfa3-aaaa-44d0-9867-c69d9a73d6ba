﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations.Schema;

namespace PerfectStore.Core.Models.DbModels;

public class PerfectEntityRule
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public required string Name { get; set; }
    public PerfectEntityRuleType RuleType { get; set; }
    public required string DisplayName { get; set; }
    public required string Visibility { get; set; }
    public string? ProductDivisionIds { get; set; }
    public long FilterConstraintId { get; set; }
    public string? Description { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public Frequency Frequency { get; set; }
    public bool IsDeactive { get; set; } = false;
    public bool Deleted { get; set; } = false;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public required string CreationContext { get; set; }
    public double Weightage { get; set; } = 0;
    public bool IsQualifier { get; set; } = false;
    public string? QualifierIds { get; set; }
    public string? QualifierRelation { get; set; }
    public string? CriteriaRelation { get; set; }

    public ICollection<PerfectEntityRuleCriteria> Criterias { get; set; }
    [ForeignKey("FilterConstraintId")]
    public FilterConstraintDetail FilterConstraintDetails { get; set; }
}


public enum PerfectEntityRuleType
{
    Store = 0,
    Call = 1,
    Entity = 2
}