﻿using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.MasterRepositories;

public class OutletMetricRepository(MasterDbContext masterDbContext) : IOutletMetricRepository
{
    public async Task<GlobalOutletMetrices?> GetOutletMetrics(long id)
    {
        return await masterDbContext.GlobalOutletMetrices
            .Where(s => s.Id == id && !s.IsDeleted)
            .FirstOrDefaultAsync();
    }
}
