﻿using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Repositories.ReportRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.ReportRepositories;

public class DmsInvoiceRepository(UnifyDbSqlDataReader unifyDbSqlDataReader) : IDmsInvoiceRepository
{
    public async Task<List<ProductSales>> GetDMSSalesProductWise(long companyId, long startDate, long endDate, long outletId)
    {
        return (await unifyDbSqlDataReader.GetModelFromQueryAsync<ProductSales>(
                    $@"select CONVERT(float, Sum(InvoiceTotalValue)) as RevenueSales, CONVERT(float, Sum(InvoiceQtyInUnits)) as QuantitySales,
                            CONVERT(float, Sum(InvoiceQtyInStdUnit)) as StdQuantitySales,
                            productid as ProductId, OutletId as OutletId from ProductWiseDemandSales
                           where companyid = {companyId} and invoicedatekey 
                          between {startDate} and {endDate} and OutletId = {outletId}
						   and InvoiceId is not null and IsInvoiceCancelled = 0
						  group by ProductId, OutletId")).ToList();
    }
}
