﻿using Libraries.CommonEnums;
using PerfectStore.Core.Models.DbModels;

namespace PerfectStore.Core.Repositories.MasterRepositories;

public interface IMasterKpiRepository
{
    Task<string?> GetKPIAchievedTarget(
        string query,
        long companyId,
        long entityId,
        FilterConstraintEntityType entityType,
        long? productDivisionId,
        DateTime startDate,
        DateTime endDate,
        int monthnumber
    );

    Task<string?> GetMasterMetricValue(
    string query,
    long companyId,
    long entityId,
    FilterConstraintEntityType entityType,
    long? productDivisionId,
    DateTime startDate,
    DateTime endDate,
    int monthNumber,
    string? parameterValue = null
    );
}
