﻿using System.ComponentModel.DataAnnotations;

namespace PerfectStore.Core.Models.DbModels
{
    public class ProductTagSuggestion
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public long OutletId { get; set; }

        public long ProductId { get; set; }

        public long SuggestedQuantity { get; set; }

        public long ProductTagId { get; set; }

        public long MTDQty { get; set; }

        public bool IsCompleted { get; set; }

        public DateTime CreatedAt { get; set; }

        [StringLength(32)]
        public string CreationContext { get; set; }

        public bool IsDeactive { get; set; }
    }
}
