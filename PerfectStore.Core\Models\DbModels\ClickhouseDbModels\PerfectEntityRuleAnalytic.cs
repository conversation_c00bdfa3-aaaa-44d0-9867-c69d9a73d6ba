﻿using Libraries.CommonEnums;

namespace PerfectStore.Core.Models.DbModels.ClickhouseDbModels;

public class PerfectEntityRuleAnalytic
{
    public long CompanyId { get; set; }

    public long DateKey { get; set; }

    public long PerfectEntityRuleId { get; set; }

    public long EntityId { get; set; }

    public string? EntityType { get; set; }

    public long ProductDivisionId { get; set; }

    public long CriteriaId { get; set; }

    public double RewardsAchieved { get; set; }

    public double AchievementPercentage { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public string? CreationContext { get; set; } = "perfect-entity-continuous-processor";

    public string? Target { get; set; }

    public string? Achievement { get; set; }

    public string? CriteriaType { get; set; }

    public long RewardId { get; set; }
}

