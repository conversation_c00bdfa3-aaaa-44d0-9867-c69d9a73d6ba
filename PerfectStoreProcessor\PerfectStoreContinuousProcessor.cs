﻿using System.Text.Json;
using Library.Infrastructure.QueueService;
using Library.ResiliencyHelpers;
using Microsoft.Azure.WebJobs;
using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Services;
using Serilog;

namespace PerfectStoreProcessor
{
    public class PerfectStoreContinuousProcessor(
        ResilientAction resilientAction,
        IPerfectStoreContinuousService perfectStoreContinuousService,
        IPerfectStoreTaskService perfectStoreTaskService,
        IFocusAreaService focusAreaService,
        IPerfectStoreTagService perfectStoreTagService
    )
    {
        /// <summary>
        /// Processes data from the FaisTaskManagmeentQueue Queue using the provided service.
        /// </summary>
        /// <param name="data">The data from the Perfect Store Continuous Queue to be processed.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task Process(
            [QueueTrigger("fais-task-management-queue", Connection = "StorageConnectionString")]
                PerfectStoreContinuousQueueData data
        )
        {
            try
            {
                await resilientAction.RetryResilientlyAsync(
                    perfectStoreContinuousService.Process,
                    data
                );
            }
            catch (Exception ex)
            {
                Log.Error(
                    ex,
                    $"Perfect Store Continuous Processor failed. Error captured over Slack for data {JsonSerializer.Serialize(data)}"
                );
                throw;
            }
        }

        /// <summary>
        /// Processes data from the Perfect Store Task Queue using the provided service.
        /// </summary>
        /// <param name="data">The data from the Perfect Store Task Queue to be processed.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task ProcessPerfectStoreTask(
            [QueueTrigger("perfectstore-task-queue", Connection = "StorageConnectionString")]
                PerfectStoreTaskQueueData data
        )
        {
            try
            {
                await resilientAction.RetryResilientlyAsync(
                    perfectStoreTaskService.ProcessQueue,
                    data
                );
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Perfect Store Task Processor failed, exception");
                throw;
            }
        }

        /// <summary>
        /// Processes data from the Perfect Store Focus Area Queue using the provided service.
        /// </summary>
        /// <param name="data">The data from the Perfect Store Focus Area Queue to be processed.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task ProcessFocusArea(
            [QueueTrigger("perfectstore-focusarea-queue", Connection = "StorageConnectionString")]
                FocusAreaQueueData data
        )
        {
            try
            {
                await resilientAction.RetryResilientlyAsync(focusAreaService.ProcessQueue, data);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Perfect Store Focus Area Processor failed");
                throw;
            }
        }

        /// <summary>
        /// Processes data from the Perfect Store Tag Queue using the provided service.
        /// </summary>
        /// <param name="data">The data from the Perfect Store Tag Queue to be processed.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task ProcessPerfectStoreTag(
            [QueueTrigger("perfectstore-tag-queue", Connection = "StorageConnectionString")]
                PerfectStoreTagQueueData data
        )
        {
            try
            {
                await resilientAction.RetryResilientlyAsync(
                    perfectStoreTagService.ProcessQueue,
                    data
                );
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Perfect Store Tag Processor failed");
                throw;
            }
        }
    }
}
