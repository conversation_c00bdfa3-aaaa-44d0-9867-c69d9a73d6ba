﻿using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Models.DbModels.TransactionDbModels;

namespace PerfectStore.Core.Repositories.TransactionRepositories
{
    public interface ITaskAchievementAndRewardsRepository
    {
        Task<List<TaskAchievementAndReward>> GetTaskAchievementAndReward(
            long companyId,
            long entityId,
            long focusAreaId
        );

        Task<TaskAchievementAndReward> GetRecentTaskAchievementAndReward(
            long companyId,
            long entityId,
            long focusAreaId
        );

        Task<List<TaskAchievementAndReward>> GetTaskAcheivementAndRewards(
            long companyId,
            DateTime startDate,
            DateTime endDate
        );
    }
}
