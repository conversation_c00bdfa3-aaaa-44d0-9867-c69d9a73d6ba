﻿using System.Text.Json;
using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.MasterRepositories
{
    public class CompanySettingRepository(MasterDbContext masterDbContext)
        : ICompanySettingRepository
    {
        public virtual Dictionary<string, object> GetSettings(long companyId)
        {
            var settingsdb = masterDbContext
                .CompanySettings.Where(s => !s.IsDeprecated)
                .GroupJoin(
                    masterDbContext
                        .CompanySettingValues.Where(v => v.CompanyId == companyId)
                        .DefaultIfEmpty(),
                    s => s.Id,
                    v => v.SettingId,
                    (s, v) =>
                        new
                        {
                            s.Setting<PERSON>ey,
                            s.SettingType,
                            s.DefaultValue,
                            v.FirstOrDefault().SettingValue,
                        }
                )
                .ToList();
            var settings = settingsdb.ToDictionary(
                k => k.Set<PERSON>,
                v => ConvertFrom(v.SettingType, v?.SettingValue, v.DefaultValue)
            );
            return settings;
        }

        public async Task<List<long>> GetAllCompanyIdsUsingPerfectStore()
        {
            var companyIds = await masterDbContext
                .CompanySettingValues.Join(
                    masterDbContext.CompanySettings,
                    r => r.SettingId,
                    p => p.Id,
                    (r, p) => new { r, p }
                )
                .Where(s =>
                    s.p.SettingKey == "CompanyUsesPerfectStore" && s.r.SettingValue == "True"
                )
                .Select(s => s.r.CompanyId)
                .ToListAsync();

            return companyIds;
        }

        public async Task<List<long>> GetAllCompanyIdsUsingPerfectEntity()
        {
            var companyIds = await masterDbContext
                .CompanySettingValues.Join(
                    masterDbContext.CompanySettings,
                    r => r.SettingId,
                    p => p.Id,
                    (r, p) => new { r, p }
                )
                .Where(s =>
                    s.p.SettingKey == "CompanyUsesPerfectEntity" && s.r.SettingValue == "True"
                )
                .Select(s => s.r.CompanyId)
                .ToListAsync();

            return companyIds;
        }

        public async Task<int> GetYearStartMonth(long companyId)
        {
            var settingId = await masterDbContext
                .CompanySettings.Where(p => p.SettingKey == "yearStartMonth")
                .FirstOrDefaultAsync();
            if (settingId != null)
            {
                var result =
                    (
                        await masterDbContext
                            .CompanySettingValues.Where(p =>
                                p.CompanyId == companyId && p.SettingId == settingId.Id
                            )
                            .Select(s => s.SettingValue)
                            .FirstOrDefaultAsync()
                    ) ?? "4";

                return int.Parse(result);
            }
            return 4;
        }

        public async Task<TimeSpan> GetTimeZoneOffset(long companyId)
        {
            var timeZoneOffsetMinutes = await (
                from cd in masterDbContext.CountryDetails
                join csv in masterDbContext.CompanySettingValues on companyId equals csv.CompanyId
                join cs in masterDbContext.CompanySettings on csv.SettingId equals cs.Id
                where cs.SettingKey == "Country"
                select cd.TimeZoneOffsetMinutes
            ).FirstOrDefaultAsync();

            return TimeSpan.FromMinutes(timeZoneOffsetMinutes);
        }

        private static object ConvertFrom(
            CompanySettingType settingType,
            string settingValue,
            string defaultValue
        )
        {
            switch (settingType)
            {
                case CompanySettingType.Boolean:
                    if (
                        !string.IsNullOrWhiteSpace(settingValue)
                        && bool.TryParse(settingValue, out var boolValue)
                    )
                        return boolValue;
                    return bool.Parse(defaultValue);

                case CompanySettingType.Decimal:
                    if (
                        !string.IsNullOrWhiteSpace(settingValue)
                        && double.TryParse(settingValue, out var decimalValue)
                    )
                        return decimalValue;
                    return double.Parse(defaultValue);

                case CompanySettingType.Integer:
                    if (
                        !string.IsNullOrWhiteSpace(settingValue)
                        && long.TryParse(settingValue, out var intValue)
                    )
                        return intValue;
                    return long.Parse(defaultValue);

                case CompanySettingType.TextList:
                    if (string.IsNullOrWhiteSpace(settingValue))
                        return new List<string>();
                    return JsonSerializer.Deserialize<List<string>>(settingValue)
                        ?? new List<string>();

                case CompanySettingType.Text:
                    return string.IsNullOrWhiteSpace(settingValue) ? defaultValue : settingValue;

                default:
                    return settingValue;
            }
        }

        private async Task<Dictionary<string, object>> ReplaceCountryDetails(
            Dictionary<string, object> configs,
            string country
        )
        {
            // Fetch the country details from the database
            var countryDetails = await masterDbContext.CountryDetails.FirstOrDefaultAsync(d =>
                d.CountryName == country
            );

            if (countryDetails == null)
            {
                throw new InvalidOperationException($"Country details for '{country}' not found.");
            }

            // Create a dictionary from the countryDetails properties
            var countryProperties = countryDetails
                .GetType()
                .GetProperties()
                .Where(p => !p.GetMethod?.IsVirtual ?? false && p.GetIndexParameters().Length == 0)
                .ToDictionary(p => p.Name, p => p.GetValue(countryDetails));

            // Update the configs dictionary with the country properties
            foreach (var property in countryProperties)
            {
                configs[property.Key] = property.Value;
            }

            return configs;
        }
    }
}
