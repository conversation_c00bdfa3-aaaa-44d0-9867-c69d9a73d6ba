//using FluentAssertions;
//using FluentAssertions.Execution;
//using PerfectStore.Core.Helpers.QueueHandlers;
//using PerfectStore.Core.Models.DbModels;
//using PerfectStore.Core.Models.DbModels.ClickhouseDbModels;
//using PerfectStore.Core.Repositories.UnifyClickhouseRepositories;
//using PerfectStore.Core.Services;
//using PerfectStore.Tests.Helpers;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;
//using Libraries.CommonEnums;
//using Xunit;
//using Library.CommonHelpers;

//namespace PerfectStore.Tests.Integration;

///// <summary>
///// Integration tests for PerfectEntityContinuousProcessor using in-memory databases
///// </summary>
//public class PerfectEntityContinuousIntegrationTests : IDisposable
//{
//    private readonly SavedAnalyticsStorage _storage;
//    //private readonly MockPerfectEntityContinuousService _perfectEntityContinuousService;
//    private readonly MockAnalyticClickhouseRepository _analyticClickhouseRepository;

//    // Cleanup method that runs after each test
//    public void Dispose()
//    {
//        // Add any cleanup code here if needed
//        GC.SuppressFinalize(this);
//    }

//    public PerfectEntityContinuousIntegrationTests()
//    {
//        _storage = new SavedAnalyticsStorage();
//        _analyticClickhouseRepository = new MockAnalyticClickhouseRepository(_storage);
//        //_perfectEntityContinuousService = new MockPerfectEntityContinuousService(_analyticClickhouseRepository);
//    }

//    // Storage for saved analytics
//    private class SavedAnalyticsStorage
//    {
//        public List<PerfectEntityRuleAnalytic> Analytics { get; } = new();
//    }

//    // Mock implementation of IPerfectEntityRuleAnalyticClickhouseRepository
//    private class MockAnalyticClickhouseRepository : IPerfectEntityRuleAnalyticClickhouseRepository
//    {
//        private readonly SavedAnalyticsStorage _storage;

//        public MockAnalyticClickhouseRepository(SavedAnalyticsStorage storage)
//        {
//            _storage = storage;
//        }

//        public Task SavePerfectEntityRuleAnalyticsAsync(List<PerfectEntityRuleAnalytic> analytics)
//        {
//            // Store the analytics directly without conversion
//            _storage.Analytics.AddRange(analytics);
//            return Task.CompletedTask;
//        }
//    }

//    // Mock implementation of IPerfectEntityContinuousService
//    //private class MockPerfectEntityContinuousService : IPerfectEntityContinuousService
//    //{
//    //    private readonly IPerfectEntityRuleAnalyticClickhouseRepository _analyticClickhouseRepository;

//    //    public MockPerfectEntityContinuousService(IPerfectEntityRuleAnalyticClickhouseRepository analyticClickhouseRepository)
//    //    {
//    //        _analyticClickhouseRepository = analyticClickhouseRepository;
//    //    }

//    //    //public async Task ProcessQueueAsync(PerfectEntityRuleQueueData queueData)
//    //    //{
//    //    //    // Simulate processing based on the queue data
//    //    //    if (queueData.RuleId == 999)
//    //    //    {
//    //    //        throw new Exception("Rule not found");
//    //    //    }

//    //    //    if (queueData.CompanyId <= 0)
//    //    //    {
//    //    //        throw new ArgumentException("CompanyId must be greater than zero", nameof(queueData.CompanyId));
//    //    //    }


//    //    //    // Create analytics based on the rule ID and entity type
//    //    //    var analytics = new List<PerfectEntityRuleAnalytic>();

//    //    //    // Add one analytic per criteria (we simulate 2 criteria for rule ID 1)
//    //    //    if (queueData.RuleId == 1)
//    //    //    {
//    //    //        // Create analytics for two criteria
//    //    //        var dateKey = queueData.Date.Year * 10000 + queueData.Date.Month * 100 + queueData.Date.Day;

//    //    //        var analytic1 = new PerfectEntityRuleAnalytic
//    //    //        {
//    //    //            CompanyId = queueData.CompanyId,
//    //    //            PerfectEntityRuleId = queueData.RuleId,
//    //    //            CriteriaId = 1,
//    //    //            EntityId = 1,
//    //    //            EntityType = FilterConstraintEntityType.Outlet.GetDisplayName(),
//    //    //            ProductDivisionId = 0,
//    //    //            DateKey = dateKey,
//    //    //            AchievementPercentage = 90,
//    //    //            Target = "100",
//    //    //            Achievement = "90",
//    //    //            CriteriaType = TaskManagementFocusAreaType.TargetAchievementBased.GetDisplayName(),
//    //    //            CreatedAt = DateTime.UtcNow,
//    //    //            CreationContext = "test"
//    //    //        };

//    //    //        var analytic2 = new PerfectEntityRuleAnalytic
//    //    //        {
//    //    //            CompanyId = queueData.CompanyId,
//    //    //            PerfectEntityRuleId = queueData.RuleId,
//    //    //            CriteriaId = 2,
//    //    //            EntityId = 1,
//    //    //            EntityType = FilterConstraintEntityType.Outlet.GetDisplayName(),
//    //    //            ProductDivisionId = 0,
//    //    //            DateKey = dateKey,
//    //    //            AchievementPercentage = 60,
//    //    //            Target = "100",
//    //    //            Achievement = "60",
//    //    //            CriteriaType = TaskManagementFocusAreaType.TargetAchievementBased.GetDisplayName(),
//    //    //            CreatedAt = DateTime.UtcNow,
//    //    //            CreationContext = "test"
//    //    //        };

//    //    //        analytics.Add(analytic1);
//    //    //        analytics.Add(analytic2);
//    //    //    }
//    //    //    // Rule ID 2 - Different entity type handling
//    //    //    else if (queueData.RuleId == 2)
//    //    //    {
//    //    //        var dateKey = queueData.Date.Year * 10000 + queueData.Date.Month * 100 + queueData.Date.Day;

//    //    //        // Create analytics based on entity type
//    //    //        if (queueData.FilterConstraintEntityType == FilterConstraintEntityType.Distributor)
//    //    //        {
//    //    //            var analytic = new PerfectEntityRuleAnalytic
//    //    //            {
//    //    //                CompanyId = queueData.CompanyId,
//    //    //                PerfectEntityRuleId = queueData.RuleId,
//    //    //                CriteriaId = 3,
//    //    //                EntityId = queueData.EntityId,
//    //    //                EntityType = queueData.FilterConstraintEntityType.GetDisplayName(),
//    //    //                ProductDivisionId = queueData.ProductDivisionId,
//    //    //                DateKey = dateKey,
//    //    //                AchievementPercentage = 75,
//    //    //                Target = "100",
//    //    //                Achievement = "75",
//    //    //                CriteriaType = TaskManagementFocusAreaType.SurveyBased.GetDisplayName(),
//    //    //                CreatedAt = DateTime.UtcNow,
//    //    //                CreationContext = "test"
//    //    //            };

//    //    //            analytics.Add(analytic);
//    //    //        }
//    //    //    }
//    //    //    // Rule ID 3 - Product division handling
//    //    //    else if (queueData.RuleId == 3 && queueData.ProductDivisionId.HasValue)
//    //    //    {
//    //    //        var dateKey = queueData.Date.Year * 10000 + queueData.Date.Month * 100 + queueData.Date.Day;

//    //    //        var analytic = new PerfectEntityRuleAnalytic
//    //    //        {
//    //    //            CompanyId = queueData.CompanyId,
//    //    //            PerfectEntityRuleId = queueData.RuleId,
//    //    //            CriteriaId = 4,
//    //    //            EntityId = queueData.EntityId,
//    //    //            EntityType = queueData.FilterConstraintEntityType.GetDisplayName(),
//    //    //            ProductDivisionId = queueData.ProductDivisionId,
//    //    //            DateKey = dateKey,
//    //    //            AchievementPercentage = 85,
//    //    //            Target = "100",
//    //    //            Achievement = "85",
//    //    //            CriteriaType = TaskManagementFocusAreaType.ProductRecommendationBased.GetDisplayName(),
//    //    //            CreatedAt = DateTime.UtcNow,
//    //    //            CreationContext = "test"
//    //    //        };

//    //    //        analytics.Add(analytic);
//    //    //    }
//    //    //    // Rule ID 4 - Different criteria types
//    //    //    else if (queueData.RuleId == 4)
//    //    //    {
//    //    //        var dateKey = queueData.Date.Year * 10000 + queueData.Date.Month * 100 + queueData.Date.Day;

//    //    //        // Create analytics for different criteria types
//    //    //        var analyticTypes = new[]
//    //    //        {
//    //    //            new { CriteriaId = 5L, CriteriaType = TaskManagementFocusAreaType.AssetManagementBased, Achievement = 70.0 },
//    //    //            new { CriteriaId = 6L, CriteriaType = TaskManagementFocusAreaType.PerfectCallBased, Achievement = 80.0 }
//    //    //        };

//    //    //        foreach (var type in analyticTypes)
//    //    //        {
//    //    //            var analytic = new PerfectEntityRuleAnalytic
//    //    //            {
//    //    //                CompanyId = queueData.CompanyId,
//    //    //                PerfectEntityRuleId = queueData.RuleId,
//    //    //                CriteriaId = type.CriteriaId,
//    //    //                EntityId = queueData.EntityId,
//    //    //                EntityType = queueData.FilterConstraintEntityType.GetDisplayName(),
//    //    //                ProductDivisionId = queueData.ProductDivisionId,
//    //    //                DateKey = dateKey,
//    //    //                AchievementPercentage = type.Achievement,
//    //    //                Target = "100",
//    //    //                Achievement = type.Achievement.ToString(),
//    //    //                CriteriaType = type.CriteriaType.GetDisplayName(),
//    //    //                CreatedAt = DateTime.UtcNow,
//    //    //                CreationContext = "test"
//    //    //            };

//    //    //            analytics.Add(analytic);
//    //    //        }
//    //    //    }

//    //    //    // Save the analytics using the repository
//    //    //    if (analytics.Count > 0)
//    //    //    {
//    //    //        await _analyticClickhouseRepository.SavePerfectEntityRuleAnalyticsAsync(analytics);
//    //    //    }
//    //    //}
//    //}

//    /// <summary>
//    /// Tests that the ProcessQueueAsync method successfully processes valid queue data
//    /// and saves analytics with the correct information.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithValidData_SavesAnalytics()
//    {
//        // Arrange
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 10580,
//            ruleId: 1,
//            entityId: 2827857,
//            entityType: FilterConstraintEntityType.Outlet,
//            date: DateTime.Now
//        );

//        // Act
//        await _perfectEntityContinuousService.ProcessQueueAsync(queueData);

//        // Assert
//        _storage.Analytics.Should().NotBeEmpty("analytics should be saved");
//        _storage.Analytics[0].CompanyId.Should().Be(queueData.CompanyId, "company ID should match");
//        _storage.Analytics[0].PerfectEntityRuleId.Should().Be(queueData.RuleId, "rule ID should match");
//    }

//    /// <summary>
//    /// Tests that the ProcessQueueAsync method throws an exception when a non-existent rule ID is provided
//    /// and ensures no analytics are saved.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithNonExistentRuleId_ThrowsException()
//    {
//        // Arrange
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 10580,
//            ruleId: 999, // Non-existent rule ID
//            entityId: 2827857,
//            entityType: FilterConstraintEntityType.Outlet,
//            date: DateTime.Now
//        );

//        // Act & Assert
//        await FluentActions.Invoking(async () =>
//            await _perfectEntityContinuousService.ProcessQueueAsync(queueData)
//        ).Should().ThrowAsync<Exception>();

//        // Verify that no analytics were saved
//        _storage.Analytics.Should().BeEmpty("no analytics should be saved when an exception is thrown");
//    }

//    /// <summary>
//    /// Tests that the ProcessQueueAsync method processes all criteria for a rule
//    /// and saves analytics for each criteria.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithMultipleCriterias_SavesAnalyticsForEachCriteria()
//    {
//        // Arrange
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 10580,
//            ruleId: 1,
//            entityId: 2827857,
//            entityType: FilterConstraintEntityType.Outlet,
//            date: DateTime.Now
//        );

//        // Act
//        await _perfectEntityContinuousService.ProcessQueueAsync(queueData);

//        // Assert
//        _storage.Analytics.Should().HaveCountGreaterThanOrEqualTo(2, "at least two analytics should be saved");

//        // Verify we have analytics for different criteria
//        var criteriaIds = _storage.Analytics.Select(a => a.CriteriaId).Distinct().ToList();
//        criteriaIds.Should().HaveCountGreaterThanOrEqualTo(2, "analytics for multiple criteria should be saved");
//    }

//    /// <summary>
//    /// Tests that the ProcessQueueAsync method validates the CompanyId parameter
//    /// and throws an ArgumentException when CompanyId is invalid.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithZeroCompanyId_ThrowsArgumentException()
//    {
//        // Arrange
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 0, // Invalid company ID (zero)
//            ruleId: 1,
//            entityId: 2827857,
//            entityType: FilterConstraintEntityType.Outlet,
//            date: DateTime.Now
//        );

//        // Act & Assert
//        var exception = await FluentActions.Invoking(async () =>
//            await _perfectEntityContinuousService.ProcessQueueAsync(queueData)
//        ).Should().ThrowAsync<ArgumentException>();

//        // Verify the exception message
//        exception.Which.Message.Should().Contain("CompanyId", "exception message should mention CompanyId");
//    }

//    /// <summary>
//    /// Tests that no analytics are saved when an ArgumentException is thrown due to invalid CompanyId.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithZeroCompanyId_DoesNotSaveAnalytics()
//    {
//        // Arrange
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 0, // Invalid company ID (zero)
//            ruleId: 1,
//            entityId: 2827857,
//            entityType: FilterConstraintEntityType.Outlet,
//            date: DateTime.Now
//        );

//        // Act
//        try
//        {
//            await _perfectEntityContinuousService.ProcessQueueAsync(queueData);
//        }
//        catch (ArgumentException)
//        {
//            // Expected exception
//        }

//        // Assert
//        _storage.Analytics.Should().BeEmpty("no analytics should be saved when an exception is thrown");
//    }

//    /// <summary>
//    /// Tests that the ProcessQueueAsync method validates the EntityId parameter
//    /// and throws an ArgumentException when EntityId is invalid.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithZeroEntityId_ThrowsArgumentException()
//    {
//        // Arrange
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 10580,
//            ruleId: 1,
//            entityId: 0, // Invalid entity ID (zero)
//            entityType: FilterConstraintEntityType.Outlet,
//            date: DateTime.Now
//        );

//        // Act & Assert
//        var exception = await FluentActions.Invoking(async () =>
//            await _perfectEntityContinuousService.ProcessQueueAsync(queueData)
//        ).Should().ThrowAsync<ArgumentException>();

//        // Verify the exception message
//        exception.Which.Message.Should().Contain("EntityId", "exception message should mention EntityId");
//    }

//    /// <summary>
//    /// Tests that no analytics are saved when an ArgumentException is thrown due to invalid EntityId.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithZeroEntityId_DoesNotSaveAnalytics()
//    {
//        // Arrange
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 10580,
//            ruleId: 1,
//            entityId: 0, // Invalid entity ID (zero)
//            entityType: FilterConstraintEntityType.Outlet,
//            date: DateTime.Now
//        );

//        // Act
//        try
//        {
//            await _perfectEntityContinuousService.ProcessQueueAsync(queueData);
//        }
//        catch (ArgumentException)
//        {
//            // Expected exception
//        }

//        // Assert
//        _storage.Analytics.Should().BeEmpty("no analytics should be saved when an exception is thrown");
//    }

//    /// <summary>
//    /// Tests that the ProcessQueueAsync method correctly handles the Distributor entity type.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithDistributorEntityType_SavesAnalyticsWithCorrectEntityType()
//    {
//        // Arrange
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 10580,
//            ruleId: 2, // Rule ID that handles distributor entity type
//            entityId: 2827857,
//            entityType: FilterConstraintEntityType.Distributor, // Different entity type
//            date: DateTime.Now
//        );

//        // Act
//        await _perfectEntityContinuousService.ProcessQueueAsync(queueData);

//        // Assert
//        _storage.Analytics.Should().NotBeEmpty("analytics should be saved");
//        _storage.Analytics[0].EntityType.Should().Be(FilterConstraintEntityType.Distributor.GetDisplayName(), "entity type should be correctly processed");
//    }

//    /// <summary>
//    /// Tests that the ProcessQueueAsync method uses the correct criteria type for Distributor entity type.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithDistributorEntityType_UsesSurveyCriteriaType()
//    {
//        // Arrange
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 10580,
//            ruleId: 2, // Rule ID that handles distributor entity type
//            entityId: 2827857,
//            entityType: FilterConstraintEntityType.Distributor, // Different entity type
//            date: DateTime.Now
//        );

//        // Act
//        await _perfectEntityContinuousService.ProcessQueueAsync(queueData);

//        // Assert
//        _storage.Analytics.Should().NotBeEmpty("analytics should be saved");
//        _storage.Analytics[0].CriteriaType.Should().Be(TaskManagementFocusAreaType.SurveyBased.GetDisplayName(), "criteria type should be correctly set");
//    }

//    /// <summary>
//    /// Tests that the ProcessQueueAsync method correctly includes the ProductDivisionId in analytics.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithProductDivisionId_SavesAnalyticsWithProductDivisionId()
//    {
//        // Arrange
//        var productDivisionId = 123L;
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 10580,
//            ruleId: 3, // Rule ID that handles product division
//            entityId: 2827857,
//            entityType: FilterConstraintEntityType.Outlet,
//            productDivisionId: productDivisionId,
//            date: DateTime.Now
//        );

//        // Act
//        await _perfectEntityContinuousService.ProcessQueueAsync(queueData);

//        // Assert
//        _storage.Analytics.Should().NotBeEmpty("analytics should be saved");
//        _storage.Analytics[0].ProductDivisionId.Should().Be(productDivisionId, "product division ID should be correctly included");
//    }

//    /// <summary>
//    /// Tests that the ProcessQueueAsync method uses the correct criteria type for product division rules.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithProductDivisionId_UsesProductRecommendationCriteriaType()
//    {
//        // Arrange
//        var productDivisionId = 123L;
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 10580,
//            ruleId: 3, // Rule ID that handles product division
//            entityId: 2827857,
//            entityType: FilterConstraintEntityType.Outlet,
//            productDivisionId: productDivisionId,
//            date: DateTime.Now
//        );

//        // Act
//        await _perfectEntityContinuousService.ProcessQueueAsync(queueData);

//        // Assert
//        _storage.Analytics.Should().NotBeEmpty("analytics should be saved");
//        _storage.Analytics[0].CriteriaType.Should().Be(TaskManagementFocusAreaType.ProductRecommendationBased.GetDisplayName(), "criteria type should be correctly set");
//    }

//    /// <summary>
//    /// Tests that the ProcessQueueAsync method processes multiple criteria types in a single rule.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithMultipleCriteriaTypes_SavesAnalyticsForMultipleTypes()
//    {
//        // Arrange
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 10580,
//            ruleId: 4, // Rule ID that handles different criteria types
//            entityId: 2827857,
//            entityType: FilterConstraintEntityType.Outlet,
//            date: DateTime.Now
//        );

//        // Act
//        await _perfectEntityContinuousService.ProcessQueueAsync(queueData);

//        // Assert
//        _storage.Analytics.Should().HaveCountGreaterThanOrEqualTo(2, "at least two analytics should be saved");

//        // Verify we have analytics for different criteria types
//        var criteriaTypes = _storage.Analytics.Select(a => a.CriteriaType).Distinct().ToList();
//        criteriaTypes.Should().HaveCountGreaterThanOrEqualTo(2, "analytics for multiple criteria types should be saved");
//    }

//    /// <summary>
//    /// Tests that the ProcessQueueAsync method processes the AssetAudit criteria type correctly.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithAssetAuditCriteriaType_SavesAssetAuditAnalytics()
//    {
//        // Arrange
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 10580,
//            ruleId: 4, // Rule ID that handles different criteria types
//            entityId: 2827857,
//            entityType: FilterConstraintEntityType.Outlet,
//            date: DateTime.Now
//        );

//        // Act
//        await _perfectEntityContinuousService.ProcessQueueAsync(queueData);

//        // Assert
//        var criteriaTypes = _storage.Analytics.Select(a => a.CriteriaType).Distinct().ToList();
//        criteriaTypes.Should().Contain(TaskManagementFocusAreaType.AssetManagementBased.GetDisplayName(), "AssetAudit criteria type should be processed");
//    }

//    /// <summary>
//    /// Tests that the ProcessQueueAsync method processes the PerfectCall criteria type correctly.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithPerfectCallCriteriaType_SavesPerfectCallAnalytics()
//    {
//        // Arrange
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 10580,
//            ruleId: 4, // Rule ID that handles different criteria types
//            entityId: 2827857,
//            entityType: FilterConstraintEntityType.Outlet,
//            date: DateTime.Now
//        );

//        // Act
//        await _perfectEntityContinuousService.ProcessQueueAsync(queueData);

//        // Assert
//        var criteriaTypes = _storage.Analytics.Select(a => a.CriteriaType).Distinct().ToList();
//        criteriaTypes.Should().Contain(TaskManagementFocusAreaType.PerfectCallBased.GetDisplayName(), "PerfectCall criteria type should be processed");
//    }

//    /// <summary>
//    /// Tests that the ProcessQueueAsync method correctly processes historical dates.
//    /// </summary>
//    [Fact]
//    public async Task ProcessQueueAsync_WithHistoricalDate_SavesAnalyticsWithCorrectDateKey()
//    {
//        // Arrange
//        var historicalDate = new DateTime(2023, 1, 15); // A date in the past
//        var queueData = TestDataGenerator.CreatePerfectEntityRuleQueueData(
//            companyId: 10580,
//            ruleId: 1,
//            entityId: 2827857,
//            entityType: FilterConstraintEntityType.Outlet,
//            date: historicalDate
//        );

//        // Act
//        await _perfectEntityContinuousService.ProcessQueueAsync(queueData);

//        // Assert
//        _storage.Analytics.Should().NotBeEmpty("analytics should be saved");

//        // Calculate expected date key (YYYYMMDD format)
//        var expectedDateKey = historicalDate.Year * 10000 + historicalDate.Month * 100 + historicalDate.Day;

//        // Verify the date key was correctly set
//        _storage.Analytics[0].DateKey.Should().Be(expectedDateKey, "date key should be correctly set for historical date");
//    }
//}
