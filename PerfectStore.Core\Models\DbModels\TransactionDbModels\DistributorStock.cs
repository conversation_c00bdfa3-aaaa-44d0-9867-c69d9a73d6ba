﻿using System.ComponentModel.DataAnnotations.Schema;

namespace PerfectStore.Core.Models.DbModels.TransactionDbModels;

public class DistributorStock
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTimeOffset DeviceTime { get; set; }

    public long DistributorId { get; set; }

    public long EmployeeId { get; set; }

    public long FieldEventId { get; set; }

    public DateTimeOffset ServerTime { get; set; }

    public int Month { get; set; }

    public int Year { get; set; }

    public bool? IsInvalid { get; set; }

    public int? Week { get; internal set; }

    [Column("PositionCodeId")]
    public long? PositionCodeId { get; set; }

    public List<DistributorStockItem> StockItems { get; set; }
}

public class DistributorStockItem
{
    public long Id { get; set; }

    public DateTime CreatedAt { get; set; }

    public long ProductId { get; set; }

    [Column("StockRecordId")]
    public long DistributorStockId { get; set; }

    public double StockValue { get; set; }

    public string AlternateCategory { get; set; }

    public double? PTD { get; set; }

    public double? PTR { get; set; }

    public long? ProductDivisionId { get; set; }
}
