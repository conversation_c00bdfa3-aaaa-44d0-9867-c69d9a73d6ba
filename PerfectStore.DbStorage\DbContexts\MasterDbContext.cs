using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;
using Location = PerfectStore.Core.Models.DbModels.Location;

namespace PerfectStore.DbStorage.DbContexts
{
    public class MasterDbContext : DbContext
    {
        public MasterDbContext(DbContextOptions<MasterDbContext> options) : base(options)
        {
        }

        public DbSet<CompanySetting> CompanySettings { get; set; }

        public DbSet<CompanySettingValue> CompanySettingValues { get; set; }

        public DbSet<CountryDetail> CountryDetails { get; set; }

        public DbSet<TaskManagementFocusArea> TaskManagementFocusAreas { get; set; }

        public DbSet<TaskManagementUserFocusArea> TaskManagementUserFocusAreas { get; set; }

        public DbSet<TaskManagementTask> TaskManagementTasks { get; set; }

        public DbSet<PerfectStoreRule> PerfectStoreRules { get; set; }

        public DbSet<ProductTagMaster> ProductTagMasters { get; set; }

        public DbSet<ProductTagSuggestion> ProductTagSuggestions { get; set; }

        public DbSet<CompanyTargetSubscriptions> CompanyTargetSubscriptions { get; set; }

        public DbSet<CompanyTargets> CompanyTargets { get; set; }

        public DbSet<TargetMaster> TargetMaster { get; set; }

        public DbSet<PerfectEntityRule> PerfectEntityRules { get; set; }

        public DbSet<PerfectEntityRuleCriteria> PerfectEntityRuleCriterias { get; set; }

        public DbSet<PerfectCriteriaSlabDetail> PerfectCriteriaSlabDetails { get; set; }

        public DbSet<PerfectEntityQualifier> PerfectEntityQualifiers { get; set; }

        public DbSet<FilterConstraintDetail> FilterConstraintDetails { get; set; }

        public DbSet<Location> Locations { get; set; }

        public DbSet<Kpi> KPIs { get; set; }

        public DbSet<CompanyKPI> CompanyKPIs { get; set; }

        public DbSet<ClientEmployee> ClientEmployees { get; set; }

        public DbSet<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }

        public DbSet<PositionCode> PositionCodes { get; set; }

        public DbSet<Distributor> FADistributors { get; set; }

        public DbSet<GlobalOutletMetrices> GlobalOutletMetrices { get; set; }

        public DbSet<DistributorBeatMappingDB> DistributorBeatMappings { get; set; }

        public DbSet<StockistStock> StockistStocks { get; set; }

        public DbSet<StockistStockItem> StockistStockItems { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder) { }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder
                .Entity<Location>()
                .ToTable("F2KLocations")
                .Property(x => x.CompanyId)
                .HasColumnName("Company");

            modelBuilder.Entity<LocationBeat>().ToTable("LocationBeats");

            modelBuilder.Entity<Region>().ToTable("Regions");

            modelBuilder
                .Entity<Location>()
                .HasOne(l => l.Beat)
                .WithMany()
                .HasForeignKey(l => l.BeatId);

            modelBuilder
                .Entity<LocationBeat>()
                .HasOne(b => b.Territory)
                .WithMany()
                .HasForeignKey(b => b.TerritoryId);

            modelBuilder
                .Entity<Territory>()
                .ToTable("Territories")
                .HasOne(t => t.TheRegion)
                .WithMany()
                .HasForeignKey(t => t.RegionId);
            // Task-Management
            modelBuilder
                .Entity<TaskManagementUserFocusArea>()
                .HasOne(ufa => ufa.TaskManagementFocusArea)
                .WithOne(fa => fa.TaskManagementUserFocusAreas)
                .HasForeignKey<TaskManagementUserFocusArea>(ufa => ufa.TaskManagementFocusAreaID);

            modelBuilder
                .Entity<TaskManagementFocusArea>()
                .HasOne(fa => fa.TaskManagementUserFocusAreas)
                .WithOne(ufa => ufa.TaskManagementFocusArea)
                .HasForeignKey<TaskManagementUserFocusArea>(ufa => ufa.TaskManagementFocusAreaID);

            modelBuilder
                .Entity<TaskManagementTask>()
                .HasOne(tt => tt.TaskManagementFocusAreas)
                .WithMany(fa => fa.TaskManagementTasks)
                .HasForeignKey(tt => tt.TaskManagementFocusAreaID);

            modelBuilder
                .Entity<ClientEmployee>()
                .Property(e => e.CompanyId)
                .HasColumnName("Company");
            modelBuilder.Entity<ClientEmployee>().Property(e => e.Guid).HasColumnName("GUID");
            modelBuilder
                .Entity<ClientEmployee>()
                .Property(e => e.PhoneNo)
                .HasColumnName("ContactNo");
        }

        public override int SaveChanges()
        {
            throw new InvalidOperationException("The context is readonly");
        }

        public override Task<int> SaveChangesAsync(
            CancellationToken cancellationToken = default(CancellationToken)
        )
        {
            throw new InvalidOperationException("The context is readonly");
        }
    }
}
