﻿using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Repositories.MasterRepositories;

namespace PerfectStore.Core.Services
{
    public interface IPerfectStoreTaskService
    {
        Task ProcessQueue(PerfectStoreTaskQueueData data);
    }

    public class PerfectStoreTaskService(
        ITaskManagementRepository taskManagementRepository,
        IProductTagRepository productTagRepository
    ) : IPerfectStoreTaskService
    {
        public async Task ProcessQueue(PerfectStoreTaskQueueData data)
        {
            var taskManagementFocusAreas =
                await taskManagementRepository.GetTaskManagementFocusAreasForProductTag(
                    data.CompanyId,
                    data.RuleId
                );

            if (taskManagementFocusAreas?.Count > 0)
            {
                foreach (var focusArea in taskManagementFocusAreas)
                {
                    var productTagSuggestedQtyDict = focusArea.EntityId.HasValue
                        ? await productTagRepository.GetProductSuggestedQuantities(
                            focusArea.EntityId.Value,
                            focusArea.CompanyId
                        )
                        : null;

                    if (productTagSuggestedQtyDict?.Count > 0)
                    {
                        await taskManagementRepository.UpdateProductSuggestiveList(
                            productTagSuggestedQtyDict,
                            focusArea.CompanyId,
                            focusArea.Id
                        );
                    }
                }
            }
        }
    }
}
