﻿using PerfectStore.Core.Models.DTOs;

namespace PerfectStore.Core.Repositories.MasterRepositories;

public interface IPositionCodeEntityMappingRepository
{
    Task<List<PositionCodeEntityMappingMin>> GetAllPositionCodesUnderUser(
        long companyId,
        List<long> positionCodeIds,
        bool isAdmin,
        bool includeUser
    );

    Task<List<PositionCodeEntityMappingMin>> GetPositionUsers(
        long companyId,
        List<long> pcIds
    );
}
