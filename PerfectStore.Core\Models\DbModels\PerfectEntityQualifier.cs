﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace PerfectStore.Core.Models.DbModels;

public class PerfectEntityQualifier
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public QualifierKpiType KPIType { get; set; }
    public long KPIId { get; set; }
    public string? Target { get; set; }
    public ComparisonOperator? ComparisionOperator { get; set; }
    public string? ParameterValues { get; set; }
}

public class ParameterValue
{
    public string? ParameterName { get; set; }
    public string? Reference { get; set; }
    public List<long>? ParameterValues { get; set; }
}

public enum QualifierKpiType
{
    GlobalKpis = 0,
    GlobalOutletMetrices = 1,
}