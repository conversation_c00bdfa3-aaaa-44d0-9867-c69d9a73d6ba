﻿using System.Text.Json;
using EFCore.BulkExtensions;
using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.MasterRepositories
{
    public class TaskManagmentRepository(
        MasterDbContext masterDbContext,
        WritableMasterDbContext writableMasterDbContext
    ) : ITaskManagementRepository
    {
        public async Task<List<TaskManagementFocusArea>> GetTaskManagementFocusAreasForProductTag(
            long companyId,
            long ruleId
        )
        {
            var data = await masterDbContext
                .TaskManagementFocusAreas.Where(s =>
                    s.CompanyId == companyId
                    && s.ParentId == ruleId
                    && s.ParentType == ParentType.PerfectStore
                    && s.Type == TaskManagementFocusAreaType.ProductTag
                )
                .ToListAsync();
            return data;
        }

        //public async Task UpdateProductSuggestiveList(Dictionary<long, List<ProductSuggestedQtyDto>> qtyDict, long companyId, long focusAreaId)
        //{
        //    var taskManagementTasks = await writableMasterDbContext.TaskManagementTasks.Where(s => s.CompanyId == companyId
        //        && s.TaskManagementFocusAreaID == focusAreaId && s.TaskEntityType == FlexibleTargetEntityType.Outlet && s.TaskEntityId.HasValue)
        //        .ToListAsync();

        //    if (taskManagementTasks.Count > 0)
        //    {
        //        int i = 0;
        //        foreach (var task in taskManagementTasks)
        //        {
        //            if (qtyDict.TryGetValue(task.TaskEntityId!.Value, out List<ProductSuggestedQtyDto>? data))
        //            {
        //                var productHierarchyIds = task.ProductHierarchyIdsList;
        //                var newSuggestions = new List<ProductSuggestedQtyDto>();
        //                if (productHierarchyIds?.Count > 0)
        //                {
        //                    foreach (var productHierarchyId in productHierarchyIds)
        //                    {
        //                        var newSuggestion = new ProductSuggestedQtyDto { ProductHierarchyId = productHierarchyId, SuggestedQty = 0 };
        //                        newSuggestions.Add(newSuggestion);
        //                    }

        //                    data.AddRange(newSuggestions);
        //                }

        //                task.ProductSuggestedQtyList = JsonSerializer.Serialize(data);
        //            }
        //        }

        //        await writableMasterDbContext.SaveChangesAsync();
        //    }
        //}


        public async Task UpdateProductSuggestiveList(
            Dictionary<long, List<ProductSuggestedQtyDto>> qtyDict,
            long companyId,
            long focusAreaId
        )
        {
            const int batchSize = 8000;
            var taskManagementTasks = await writableMasterDbContext
                .TaskManagementTasks.Where(s =>
                    s.CompanyId == companyId
                    && s.TaskManagementFocusAreaID == focusAreaId
                    && s.TaskEntityType == FlexibleTargetEntityType.Outlet
                    && s.TaskEntityId.HasValue
                )
                .ToListAsync();

            if (taskManagementTasks.Count > 0)
            {
                int i = 0;
                while (i < taskManagementTasks.Count)
                {
                    var batchTasks = taskManagementTasks.Skip(i).Take(batchSize).ToList();

                    foreach (var task in batchTasks)
                    {
                        if (
                            qtyDict.TryGetValue(
                                task.TaskEntityId!.Value,
                                out List<ProductSuggestedQtyDto>? data
                            )
                        )
                        {
                            var productHierarchyIds = task.ProductHierarchyIdsList;
                            var newSuggestions = new List<ProductSuggestedQtyDto>();
                            if (productHierarchyIds?.Count > 0)
                            {
                                foreach (var productHierarchyId in productHierarchyIds)
                                {
                                    var newSuggestion = new ProductSuggestedQtyDto
                                    {
                                        ProductHierarchyId = productHierarchyId,
                                        SuggestedQty = 0,
                                    };
                                    newSuggestions.Add(newSuggestion);
                                }

                                data.AddRange(newSuggestions);
                            }

                            task.ProductSuggestedQtyList = JsonSerializer.Serialize(data);
                        }
                    }

                    await writableMasterDbContext.BulkUpdateAsync(batchTasks);

                    i += batchSize;
                }
            }
        }

        public async Task<List<TaskManagementTask>> GetTaskManagementTasks(
            long companyId,
            List<long> focusAreaIds
        )
        {
            return await masterDbContext
                .TaskManagementTasks.Where(s =>
                    s.CompanyId == companyId
                    && s.TaskManagementFocusAreaID.HasValue
                    && focusAreaIds.Contains(s.TaskManagementFocusAreaID.Value)
                )
                .ToListAsync();
        }

        public async Task<List<FocusAreasDto>> GetTaskManagementFocusAreasWithTasks(
            long companyId,
            long ruleId
        )
        {
            var data = await (
                from fa in masterDbContext.TaskManagementFocusAreas
                join ta in masterDbContext.TaskManagementTasks
                    on fa.Id equals ta.TaskManagementFocusAreaID
                where
                    fa.CompanyId == companyId
                    && ta.CompanyId == companyId
                    && fa.ParentId == ruleId
                    && ta.TaskEntityType == FlexibleTargetEntityType.Outlet
                    && ta.TaskEntityId.HasValue
                select new FocusAreasDto
                {
                    CompanyId = fa.CompanyId,
                    FocusAreaId = fa.Id,
                    EntityId = fa.EntityId,
                    TaskEntityId = ta.TaskEntityId,
                    TaskType = fa.Type ?? TaskManagementFocusAreaType.NumberBased,
                    TaskTarget = ta.TaskTarget,
                    TaskId = ta.Id,
                    Achievement = ta.Achievement ?? 0,
                }
            ).ToListAsync();
            return data;
        }

        public async Task<List<TaskManagementFocusArea>> GetTaskManagementFocusAreas(
            long companyId,
            List<long> ruleId
        )
        {
            return await masterDbContext
                .TaskManagementFocusAreas.Where(s =>
                    s.CompanyId == companyId && ruleId.Contains(s.ParentId)
                )
                .ToListAsync();
        }

        public async Task<TaskManagementFocusArea?> GetCompeleteTaskManagement(long companyId, long focusAreaId, long entityId)
        {
            var focusArea = await masterDbContext.TaskManagementFocusAreas
                .Where(s => s.CompanyId == companyId && s.Id == focusAreaId)
                .Select(fa => new TaskManagementFocusArea
                {
                    Id = fa.Id,
                    Name = fa.Name,
                    CompanyId = fa.CompanyId,
                    // Add other needed properties
                    TaskManagementTasks = fa.TaskManagementTasks
                        .Where(t => (t.IsDeactive == null || t.IsDeactive == false) && t.TaskEntityId == entityId)
                        .ToList()
                })
                .FirstOrDefaultAsync();

            return focusArea;
        }


        public async Task UpdateTaskAchievement(long companyId, Dictionary<long, (double,double)> taskAchievementDict)
        {
            var taskManagementTasks = await writableMasterDbContext.TaskManagementTasks
                .Where(s => s.CompanyId == companyId && taskAchievementDict.Keys.Contains(s.Id))
                .ToListAsync();

            foreach (var task in taskManagementTasks)
            {
                if (taskAchievementDict.TryGetValue(task.Id, out (double, double) targetAchievementTuple))
                {
                    task.Achievement = (long)targetAchievementTuple.Item2;
                }
            }
            await writableMasterDbContext.SaveChangesAsync();
        }

        public async Task UpdateTaskSoldProductQuantites(long companyId, Dictionary<long, List<TaskManagementSoldProductQuantity>> updatedData)
        {
            var taskIds = updatedData.Keys.ToList();

            var existingItems = await writableMasterDbContext.TaskManagementSoldProductQuantities
                .Where(s => s.CompanyId == companyId && taskIds.Contains(s.TaskManagementTaskId))
                .ToListAsync();

            if (existingItems?.Count > 0)
            {
                var existingLookup = existingItems
                    .ToDictionary(x => (x.TaskManagementTaskId, x.ProductLevelHieararchyId, x.TaskLevelHieararchyId));

                foreach (var taskId in updatedData.Keys)
                {
                    foreach (var newItem in updatedData[taskId])
                    {
                        var key = (newItem.TaskManagementTaskId, newItem.ProductLevelHieararchyId, newItem.TaskLevelHieararchyId);

                        if (existingLookup.TryGetValue(key, out var existingItem))
                        {
                            // Update fields
                            existingItem.BilledQty = newItem.BilledQty;
                            existingItem.RemainingBilledQty = newItem.RemainingBilledQty;
                            existingItem.ShowTagInApp = newItem.ShowTagInApp;
                            existingItem.LastUpdatedAt = DateTime.UtcNow;
                        }
                        else
                        {
                            // Add new if not found
                            await writableMasterDbContext.TaskManagementSoldProductQuantities.AddAsync(newItem);
                        }
                    }
                }
            }
            else
            {
                // Bulk insert if none exist
                await writableMasterDbContext.TaskManagementSoldProductQuantities
                    .AddRangeAsync(updatedData.Values.SelectMany(s => s).ToList());
            }

            await writableMasterDbContext.SaveChangesAsync();
        }

    }
}
