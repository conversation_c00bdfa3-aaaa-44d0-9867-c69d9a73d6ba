﻿using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Repositories.ReportRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.ReportRepositories;

public class AttendanceRepository(ReportDbSqlDataReader reportDbSqlDataReader) : IAttendanceRepository
{
    public async Task<List<ProductSales>> GetDispatchSalesProductWise(long companyId, long startDate, long endDate, long outletId)
    {
        return (await reportDbSqlDataReader.GetModelFromQueryAsync<ProductSales>(
                    $@"select CONVERT(float, Sum(dispatchinrevenue)) as RevenueSales,
                        CONVERT(float, Sum(DispatchInStdUnits)) as StdQuantitySales,
                        CONVERT(float, Sum(DispatchInUnits)) as QuantitySales,
                        ProductId as ProductId, LocationId as ProductId from productwisesales
                         where companyid = {companyId} and callstartdatekey 
                        between {startDate} and {endDate} and LocationId = {outletId} and DispatchInUnits > 0
						group by ProductId, LocationId")).ToList();
    }

    public async Task<List<ProductSales>> GetOutletSalesProductWise(long companyId, long startDate, long endDate, long outletId)
    {
        return (await reportDbSqlDataReader.GetModelFromQueryAsync<ProductSales>(
                    $@"select CONVERT(float, Sum(OrderInRevenue)) as RevenueSales,
                        CONVERT(float, Sum(OrderInStdUnits)) as StdQuantitySales,
                        CONVERT(float, Sum(OrderInUnits)) as QuantitySales,
                        ProductId as ProductId, LocationId as OutletId from productwisesales
                         where companyid = {companyId} and callstartdatekey 
                        between {startDate} and {endDate} and LocationId = {outletId}
						group by ProductId, LocationId")).ToList();
    }
}
