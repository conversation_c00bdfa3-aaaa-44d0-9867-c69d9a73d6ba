﻿namespace PerfectStore.Core.Models.DTOs;

public class ProductSales
{
    public long OutletId { get; set; }

    public long ProductId { get; set; }

    public double RevenueSales { get; set; }

    public double QuantitySales { get; set; }

    public double StdQuantitySales { get; set; }
}

public class ProductDto
{
    public long Id { get; set; }

    public int? MOQ { get; set; }

    public long? ProductDisplayCategoryId { get; set; }

    public long? ProductGroupId { get; set; }
}
