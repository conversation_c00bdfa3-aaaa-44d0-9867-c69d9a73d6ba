﻿using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Models.DbModels.TransactionDbModels;

namespace PerfectStore.DbStorage.DbContexts
{
    public class TransactionDbContext(DbContextOptions<TransactionDbContext> options)
        : DbContext(options)
    {
        public DbSet<TaskAchievementAndReward> TaskAchievementAndRewards { get; set; }
        public DbSet<FocusAreaOutletStatus> FocusAreaOutletStatuses { get; set; }
        public DbSet<PerfectStoreOutletStatus> PerfectStoreOutletStatus { get; set; }
        public DbSet<PerfectEntityCallDetail> PerfectEntityCallDetails { get;set; }
        public DbSet<DistributorStock> DistributorStocks { get; set; }
        public DbSet<DistributorStockItem> DistributorStockItems { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder) { }

        protected override void OnModelCreating(ModelBuilder modelBuilder) { }

        public override int SaveChanges()
        {
            throw new InvalidOperationException("The context is readonly");
        }

        public override Task<int> SaveChangesAsync(
            CancellationToken cancellationToken = default(CancellationToken)
        )
        {
            throw new InvalidOperationException("The context is readonly");
        }
    }
}
