﻿using Libraries.CommonEnums;

namespace PerfectStore.Core.Models.DTOs;

public class PositionCodeEntityMappingMin
{
    public long EntityId { get; set; }
    public bool IsDetached { get; set; }
    public PortalUserRole EntityRole { get; set; }
    public PositionCodeLevel PositionCodeLevel { get; set; }
    public string PositionCode { get; set; }
    public string Position { get; set; }
    public string UserName { get; set; }
    public long PositionId { get; set; }
    public long? ParentPositionId { get; set; }
}