﻿using Libraries.CommonEnums;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.Core.Repositories.ReportRepositories;
using PerfectStore.Core.Repositories.TransactionRepositories;
using PerfectStore.Core.Repositories.UnifyClickhouseRepositories;

namespace PerfectStore.Core.Services;

public class KpiService(
    IKpiRepository kpiRepository,
    IMasterKpiRepository masterKpiRepository,
    IReportKpiRepostory reportKpiRepostory,
    ITransactionKpiRepository transactionKpiRepository,
    IClickhouseKpiRepository clickhouseKpiRepository
)
{
    public async Task<KpiCheckModel> ProcessGlobalOutletMetrices(
        long companyId,
        long entityId,
        long? pdId,
        FilterConstraintEntityType entityType,
        PerfectEntityQualifier qualifier,
        DateTime todayDate,
        DateTime startDate,
        DateTime endDate,
        int monthNumber,
        GlobalOutletMetrices? globalOutletMetric
    )
    {
        var res = new KpiCheckModel
        {
            IsPassed = false,
            AchValue = null,
            AchPercentage = null
        };

        if (globalOutletMetric == null)
        {
            return res;
        }

        switch (globalOutletMetric.QueriesRelation)
        {
            case OutletMetricQueryRelation.OnlyReportQuery:
                res.AchValue = await reportKpiRepostory.GetReportMetricValue(globalOutletMetric.ReportSqlQuery,
                    companyId, entityId, entityType, pdId, startDate, todayDate, monthNumber, qualifier.ParameterValues);
                break;

            case OutletMetricQueryRelation.OnlyMasterQuery:
                res.AchValue = await masterKpiRepository.GetMasterMetricValue(globalOutletMetric.MasterSqlQuery,
                    companyId, entityId, entityType, pdId, startDate, todayDate, monthNumber, qualifier.ParameterValues);
                break;

            case OutletMetricQueryRelation.ReportQueryDividedbyMasterQuery:

                var reportMetericForReportByMaster = await reportKpiRepostory.GetReportMetricValue(globalOutletMetric.ReportSqlQuery,
                    companyId, entityId, entityType, pdId, startDate, todayDate, monthNumber, qualifier.ParameterValues);

                var masterMetricForReportByMaster = await masterKpiRepository.GetMasterMetricValue(globalOutletMetric.MasterSqlQuery,
                    companyId, entityId, entityType, pdId, startDate, todayDate, monthNumber, qualifier.ParameterValues);

                res.AchValue = GetMetricValueForDb1DividedByDb2(reportMetericForReportByMaster, masterMetricForReportByMaster).ToString();

                break;

            case OutletMetricQueryRelation.OnlyTransactionQuery:
                res.AchValue = await transactionKpiRepository.GetTransactionMetricValue(globalOutletMetric.TransactionSqlQuery,
                    companyId, entityId, entityType, pdId, startDate, todayDate, monthNumber, qualifier.ParameterValues);
                break;
            case OutletMetricQueryRelation.TransactionQueryDividedByMasterQuery:

                var transactionMetricForTransactionByMaster = await transactionKpiRepository.GetTransactionMetricValue(globalOutletMetric.TransactionSqlQuery,
                    companyId, entityId, entityType, pdId, startDate, todayDate, monthNumber, qualifier.ParameterValues);
                var masterMetricForTransactionByMaster = await masterKpiRepository.GetMasterMetricValue(globalOutletMetric.MasterSqlQuery,
                    companyId, entityId, entityType, pdId, startDate, todayDate, monthNumber, qualifier.ParameterValues);

                res.AchValue = GetMetricValueForDb1DividedByDb2(transactionMetricForTransactionByMaster, masterMetricForTransactionByMaster).ToString();
                break;
            case OutletMetricQueryRelation.OnlyClickhouseQuery:
                res.AchValue = await clickhouseKpiRepository.GetClickhouseMetricValue(globalOutletMetric.ClickHouseQuery,
                    companyId, entityId, entityType, pdId, startDate, todayDate, monthNumber, qualifier.ParameterValues);
                break;
        }

        // Compare achieved value with target value based on data type and comparison operator
        if (globalOutletMetric.DataType.HasValue && !string.IsNullOrEmpty(res.AchValue))
        {
            switch (globalOutletMetric.DataType.Value)
            {
                case DataTypeEnum.Decimal:
                case DataTypeEnum.WholeNumber:
                case DataTypeEnum.Percentage:
                case DataTypeEnum.Currency:
                    double.TryParse(qualifier.Target, out double targetNumValue);
                    double achievedNumValue = double.TryParse(res.AchValue, out var achievedDouble) ? achievedDouble : 0;

                    switch (qualifier.ComparisionOperator)
                    {
                        case ComparisonOperator.EqualTo:
                            res.IsPassed = achievedNumValue == targetNumValue;
                            res.AchPercentage = targetNumValue != 0 ? Math.Round(achievedNumValue / targetNumValue * 100, 2) : 0;
                            break;

                        case ComparisonOperator.LessThan:
                            res.IsPassed = achievedNumValue < targetNumValue;
                            res.AchPercentage = targetNumValue != 0 ? Math.Round(achievedNumValue / targetNumValue * 100, 2) : 0;
                            break;

                        case ComparisonOperator.LessThanOrEqualTo:
                            res.IsPassed = achievedNumValue <= targetNumValue;
                            res.AchPercentage = targetNumValue != 0 ? Math.Round(achievedNumValue / targetNumValue * 100, 2) : 0;
                            break;

                        case ComparisonOperator.GreaterThanOrEqualTo:
                            res.IsPassed = achievedNumValue >= targetNumValue;
                            res.AchPercentage = targetNumValue != 0 ? Math.Round(achievedNumValue / targetNumValue * 100, 2) : 0;
                            break;

                        case ComparisonOperator.GreaterThan:
                            res.IsPassed = achievedNumValue > targetNumValue;
                            res.AchPercentage = targetNumValue != 0 ? Math.Round(achievedNumValue / targetNumValue * 100, 2) : 0;
                            break;

                        case ComparisonOperator.NotEqualTo:
                            res.IsPassed = achievedNumValue != targetNumValue;
                            res.AchPercentage = targetNumValue != 0 ? Math.Round(achievedNumValue / targetNumValue * 100, 2) : 0;
                            break;
                    }
                    break;

                case DataTypeEnum.Time:
                    DateTime.TryParse(qualifier.Target, out DateTime targetTimeValue);
                    DateTime achievedTimeValue = DateTime.TryParse(res.AchValue, out var achievedTime) ? achievedTime : new DateTime();

                    switch (qualifier.ComparisionOperator)
                    {
                        case ComparisonOperator.EqualTo:
                            res.IsPassed = achievedTimeValue == targetTimeValue;
                            res.AchPercentage = (targetTimeValue.Hour * 60 + targetTimeValue.Minute) != 0 ?
                                (achievedTimeValue.Hour * 60 + achievedTimeValue.Minute) / (double)(targetTimeValue.Hour * 60 + targetTimeValue.Minute) * 100 : 0;
                            break;

                        case ComparisonOperator.LessThan:
                            res.IsPassed = achievedTimeValue < targetTimeValue;
                            res.AchPercentage = (targetTimeValue.Hour * 60 + targetTimeValue.Minute) != 0 ?
                                (achievedTimeValue.Hour * 60 + achievedTimeValue.Minute) / (double)(targetTimeValue.Hour * 60 + targetTimeValue.Minute) * 100 : 0;
                            break;

                        case ComparisonOperator.LessThanOrEqualTo:
                            res.IsPassed = achievedTimeValue <= targetTimeValue;
                            res.AchPercentage = (targetTimeValue.Hour * 60 + targetTimeValue.Minute) != 0 ?
                                (achievedTimeValue.Hour * 60 + achievedTimeValue.Minute) / (double)(targetTimeValue.Hour * 60 + targetTimeValue.Minute) * 100 : 0;
                            break;

                        case ComparisonOperator.GreaterThanOrEqualTo:
                            res.IsPassed = achievedTimeValue >= targetTimeValue;
                            res.AchPercentage = (targetTimeValue.Hour * 60 + targetTimeValue.Minute) != 0 ?
                                (achievedTimeValue.Hour * 60 + achievedTimeValue.Minute) / (double)(targetTimeValue.Hour * 60 + targetTimeValue.Minute) * 100 : 0;
                            break;

                        case ComparisonOperator.GreaterThan:
                            res.IsPassed = achievedTimeValue > targetTimeValue;
                            res.AchPercentage = (targetTimeValue.Hour * 60 + targetTimeValue.Minute) != 0 ?
                                (achievedTimeValue.Hour * 60 + achievedTimeValue.Minute) / (double)(targetTimeValue.Hour * 60 + targetTimeValue.Minute) * 100 : 0;
                            break;

                        case ComparisonOperator.NotEqualTo:
                            res.IsPassed = achievedTimeValue != targetTimeValue;
                            res.AchPercentage = (targetTimeValue.Hour * 60 + targetTimeValue.Minute) != 0 ?
                                (achievedTimeValue.Hour * 60 + achievedTimeValue.Minute) / (double)(targetTimeValue.Hour * 60 + targetTimeValue.Minute) * 100 : 0;
                            break;
                    }
                    break;

                case DataTypeEnum.Boolean:
                    bool.TryParse(qualifier.Target, out bool targetBoolValue);
                    bool achievedBoolValue = bool.TryParse(res.AchValue, out var achievedBool) ? achievedBool : false;

                    switch (qualifier.ComparisionOperator)
                    {
                        case ComparisonOperator.EqualTo:
                            res.IsPassed = achievedBoolValue == targetBoolValue;
                            res.AchPercentage = achievedBoolValue ? 100 : 0;
                            break;

                        case ComparisonOperator.NotEqualTo:
                            res.IsPassed = achievedBoolValue != targetBoolValue;
                            res.AchPercentage = achievedBoolValue ? 100 : 0;
                            break;
                    }
                    break;

                case DataTypeEnum.String:
                    string achievedStringValue = res.AchValue;

                    switch (qualifier.ComparisionOperator)
                    {
                        case ComparisonOperator.EqualTo:
                            res.IsPassed = achievedStringValue == qualifier.Target;
                            break;

                        case ComparisonOperator.NotEqualTo:
                            res.IsPassed = achievedStringValue != qualifier.Target;
                            break;
                    }
                    break;
            }
        }

        return res;
    }

    private static double GetMetricValueForDb1DividedByDb2(string db1MetricValue, string db2MetricValue)
    {
        var db1MetricValueInDouble = double.TryParse(db1MetricValue, out var transactionTemp)
                                                       ? transactionTemp : 0;

        var db2MetricValueInDouble = double.TryParse(db2MetricValue, out var masterTempForTransactionByMaster)
                                                 ? masterTempForTransactionByMaster : 0;

        return db2MetricValueInDouble != 0 ? (db1MetricValueInDouble / db2MetricValueInDouble) : 0;
    }

    public async Task<KpiCheckModel> ProcessGlobalKpisQualifier(
        long companyId,
        long entityId,
        long? pdId,
        FilterConstraintEntityType entityType,
        PerfectEntityQualifier qualifier,
        DateTime todayDate,
        DateTime startDate,
        DateTime endDate,
        int monthNumber,
        Kpi? kpi
    )
    {
        var result = new KpiCheckModel
        {
            IsPassed = false,
            AchValue = null,
            AchPercentage = null
        };

        if (kpi == null)
        {
            return result;
        }

        switch (kpi.Frequency)
        {
            case KpiFrequency.Daily:
                startDate = todayDate.Date;
                endDate = todayDate.AddDays(1).AddMilliseconds(-1);
                break;

            case KpiFrequency.Weekly:
                int daysUntilkpiWeekDay =
                    ((int)(kpi.WeekOfDay ?? DayOfWeek.Monday) - (int)todayDate.DayOfWeek + 7) % 7;
                endDate = todayDate.AddDays(daysUntilkpiWeekDay);
                startDate = endDate.AddDays(-7);
                break;
        }

        var reportAchValue = string.Empty;
        var transactionAchValue = string.Empty;
        var masterAchvalue = string.Empty;
        if (!string.IsNullOrEmpty(kpi.SQLQuery))
        {
            reportAchValue = await reportKpiRepostory.GetKPIAchievedTarget(
                kpi.SQLQuery,
                companyId,
                entityId,
                entityType,
                pdId,
                startDate,
                endDate,
                monthNumber
            );

            if (reportAchValue == null || (reportAchValue != null && string.IsNullOrWhiteSpace(reportAchValue)))
            {
                return result;
            }
        }

        if (!string.IsNullOrEmpty(kpi.TransactionSQLQuery))
        {
            transactionAchValue = await transactionKpiRepository.GetKPIAchievedTarget(
                kpi.TransactionSQLQuery,
                companyId,
                entityId,
                entityType,
                pdId,
                startDate,
                endDate,
                monthNumber
            );

            if (transactionAchValue == null || (transactionAchValue != null && string.IsNullOrWhiteSpace(transactionAchValue))
            )
            {
                return result;
            }
        }

        if (!string.IsNullOrEmpty(kpi.MasterSQLQuery))
        {
            masterAchvalue = await masterKpiRepository.GetKPIAchievedTarget(
                kpi.MasterSQLQuery,
                companyId,
                entityId,
                entityType,
                pdId,
                startDate,
                endDate,
                monthNumber
            );

            if (masterAchvalue == null || (masterAchvalue != null && string.IsNullOrWhiteSpace(masterAchvalue))
            )
            {
                return result;
            }
        }

        switch (kpi.Measure)
        {
            case KpiMeasure.Currency:
            case KpiMeasure.Decimal:
            case KpiMeasure.PercentageNumber:
            case KpiMeasure.Percentage:
            case KpiMeasure.Number:
                double.TryParse(qualifier.Target, out double matchNumValue);
                double achievedValue = 0;
                switch (kpi.Relation)
                {
                    case Relation.UseDirectReportDb:
                        achievedValue = double.TryParse(
                            reportAchValue,
                            out var reportAchValueDouble
                        )
                            ? Math.Round(reportAchValueDouble, 2)
                            : 0;
                        break;

                    case Relation.UseDirectMasterDb:
                        achievedValue = double.TryParse(
                            masterAchvalue,
                            out var masterAchValueDouble
                        )
                            ? Math.Round(masterAchValueDouble, 2)
                            : 0;
                        break;

                    case Relation.UseDirectTransactionDb:
                        achievedValue = double.TryParse(
                            transactionAchValue,
                            out var transactionAchValueDouble
                        )
                            ? Math.Round(transactionAchValueDouble, 2)
                            : 0;
                        break;

                    case Relation.ReportDbDividedByMasterDb:
                        if (
                            double.TryParse(reportAchValue, out reportAchValueDouble)
                            && double.TryParse(masterAchvalue, out masterAchValueDouble)
                            && !masterAchValueDouble.Equals(0)
                        )
                        {
                            achievedValue = Math.Round(
                                reportAchValueDouble / masterAchValueDouble,
                                2
                            );
                        }

                        break;

                    case Relation.MasterDbDividedByReportDb:
                        if (
                            double.TryParse(masterAchvalue, out masterAchValueDouble)
                            && double.TryParse(reportAchValue, out reportAchValueDouble)
                            && !reportAchValueDouble.Equals(0)
                        )
                        {
                            achievedValue = Math.Round(
                                masterAchValueDouble / reportAchValueDouble,
                                2
                            );
                        }

                        break;

                    case Relation.MasterDbDividedByTransactionDb:
                        if (
                            double.TryParse(masterAchvalue, out masterAchValueDouble)
                            && double.TryParse(transactionAchValue, out transactionAchValueDouble)
                            && !transactionAchValueDouble.Equals(0)
                        )
                        {
                            achievedValue = Math.Round(
                                masterAchValueDouble / transactionAchValueDouble,
                                2
                            );
                        }

                        break;

                    case Relation.TransactionDbDividedByMasterDb:
                        if (
                            double.TryParse(transactionAchValue, out transactionAchValueDouble)
                            && double.TryParse(masterAchvalue, out masterAchValueDouble)
                            && !masterAchValueDouble.Equals(0)
                        )
                        {
                            achievedValue = Math.Round(
                                transactionAchValueDouble / masterAchValueDouble,
                                2
                            );
                        }

                        break;
                }

                switch (qualifier.ComparisionOperator)
                {
                    case ComparisonOperator.EqualTo:
                        result.IsPassed = achievedValue == matchNumValue;
                        result.AchValue = achievedValue.ToString();
                        result.AchPercentage = Math.Round(achievedValue / matchNumValue * 100, 2);
                        break;

                    case ComparisonOperator.LessThan:
                        result.IsPassed = achievedValue < matchNumValue;
                        result.AchValue = achievedValue.ToString();
                        result.AchPercentage = Math.Round(achievedValue / matchNumValue * 100, 2);
                        break;

                    case ComparisonOperator.LessThanOrEqualTo:
                        result.IsPassed = achievedValue <= matchNumValue;
                        result.AchValue = achievedValue.ToString();
                        result.AchPercentage = Math.Round(achievedValue / matchNumValue * 100, 2);
                        break;

                    case ComparisonOperator.GreaterThanOrEqualTo:
                        result.IsPassed = achievedValue >= matchNumValue;
                        result.AchValue = achievedValue.ToString();
                        result.AchPercentage = Math.Round(achievedValue / matchNumValue * 100, 2);
                        break;

                    case ComparisonOperator.GreaterThan:
                        result.IsPassed = achievedValue > matchNumValue;
                        result.AchValue = achievedValue.ToString();
                        result.AchPercentage = Math.Round(achievedValue / matchNumValue * 100, 2);
                        break;

                    case ComparisonOperator.NotEqualTo:
                        result.IsPassed = achievedValue != matchNumValue;
                        result.AchValue = achievedValue.ToString();
                        result.AchPercentage = Math.Round(achievedValue / matchNumValue * 100, 2);
                        break;
                }

                break;

            case KpiMeasure.Time:
                DateTime.TryParse(qualifier.Target, out DateTime matchTimeValue);
                DateTime achievedValueTime = new DateTime();
                switch (kpi.Relation)
                {
                    case Relation.UseDirectReportDb:
                        achievedValueTime = DateTime.Parse(reportAchValue);
                        break;

                    case Relation.UseDirectMasterDb:
                        achievedValueTime = DateTime.Parse(masterAchvalue);
                        break;

                    case Relation.UseDirectTransactionDb:
                        achievedValueTime = DateTime.Parse(transactionAchValue);
                        break;
                }

                switch (qualifier.ComparisionOperator)
                {
                    case ComparisonOperator.EqualTo:
                        result.IsPassed = achievedValueTime == matchTimeValue;
                        result.AchValue = achievedValueTime.ToString("HH:mm");
                        result.AchPercentage = (
                            (achievedValueTime.Hour * 60 + achievedValueTime.Minute)
                            / (matchTimeValue.Hour * 60 + matchTimeValue.Minute) * 100
                        );
                        break;

                    case ComparisonOperator.LessThan:
                        result.IsPassed = achievedValueTime < matchTimeValue;
                        result.AchValue = achievedValueTime.ToString("HH:mm");
                        result.AchPercentage = (
                         (achievedValueTime.Hour * 60 + achievedValueTime.Minute)
                         / (matchTimeValue.Hour * 60 + matchTimeValue.Minute) * 100
                     );
                        break;

                    case ComparisonOperator.LessThanOrEqualTo:
                        result.IsPassed = achievedValueTime <= matchTimeValue;
                        result.AchValue = achievedValueTime.ToString("HH:mm");
                        result.AchPercentage = (
                         (achievedValueTime.Hour * 60 + achievedValueTime.Minute)
                         / (matchTimeValue.Hour * 60 + matchTimeValue.Minute) * 100
                     );
                        break;

                    case ComparisonOperator.GreaterThanOrEqualTo:
                        result.IsPassed = achievedValueTime >= matchTimeValue;
                        result.AchValue = achievedValueTime.ToString("HH:mm");
                        result.AchPercentage = (
                         (achievedValueTime.Hour * 60 + achievedValueTime.Minute)
                         / (matchTimeValue.Hour * 60 + matchTimeValue.Minute) * 100
                     );
                        break;

                    case ComparisonOperator.GreaterThan:
                        result.IsPassed = achievedValueTime > matchTimeValue;
                        result.AchValue = achievedValueTime.ToString("HH:mm");
                        result.AchPercentage = (
                         (achievedValueTime.Hour * 60 + achievedValueTime.Minute)
                         / (matchTimeValue.Hour * 60 + matchTimeValue.Minute) * 100
                     );
                        break;

                    case ComparisonOperator.NotEqualTo:
                        result.IsPassed = achievedValueTime != matchTimeValue;
                        result.AchValue = achievedValueTime.ToString("HH:mm");
                        result.AchPercentage = (
                         (achievedValueTime.Hour * 60 + achievedValueTime.Minute)
                         / (matchTimeValue.Hour * 60 + matchTimeValue.Minute) * 100
                     );
                        break;
                }

                break;

            case KpiMeasure.YesNo:
                bool achievedValueBool = false;
                Boolean.TryParse(qualifier.Target, out var matchValueBool);
                switch (kpi.Relation)
                {
                    case Relation.UseDirectReportDb:
                        achievedValueBool = Convert.ToBoolean(reportAchValue);
                        break;

                    case Relation.UseDirectMasterDb:
                        achievedValueBool = Convert.ToBoolean(masterAchvalue);
                        break;

                    case Relation.UseDirectTransactionDb:
                        achievedValueBool = Convert.ToBoolean(transactionAchValue);
                        break;
                }

                switch (qualifier.ComparisionOperator)
                {
                    case ComparisonOperator.EqualTo:
                        result.IsPassed = achievedValueBool == matchValueBool;
                        result.AchValue = achievedValueBool.ToString();
                        result.AchPercentage = achievedValueBool ? 100 : 0;
                        break;

                    case ComparisonOperator.NotEqualTo:
                        result.IsPassed = achievedValueBool != matchValueBool;
                        result.AchValue = achievedValueBool.ToString();
                        result.AchPercentage = achievedValueBool ? 100 : 0;
                        break;
                }

                break;

            case KpiMeasure.TimeDuration:
                TimeSpan.TryParse(qualifier.Target, out TimeSpan matchTimeSpanValue);
                TimeSpan achievedValueTimeSpan = new TimeSpan(0, 0, 0);
                switch (kpi.Relation)
                {
                    case Relation.UseDirectReportDb:
                        achievedValueTimeSpan = TimeSpan.Parse(reportAchValue);
                        break;

                    case Relation.UseDirectMasterDb:
                        achievedValueTimeSpan = TimeSpan.Parse(masterAchvalue);
                        break;

                    case Relation.UseDirectTransactionDb:
                        achievedValueTimeSpan = TimeSpan.Parse(transactionAchValue);
                        break;
                }

                switch (qualifier.ComparisionOperator)
                {
                    case ComparisonOperator.EqualTo:
                        result.IsPassed = achievedValueTimeSpan == matchTimeSpanValue;
                        result.AchValue = achievedValueTimeSpan.ToString(@"HH\:mm");
                        result.AchPercentage = (
                            (achievedValueTimeSpan.Hours * 60 + achievedValueTimeSpan.Minutes)
                            / (matchTimeSpanValue.Hours * 60 + matchTimeSpanValue.Minutes) * 100
                        );
                        break;

                    case ComparisonOperator.LessThan:
                        result.IsPassed = achievedValueTimeSpan < matchTimeSpanValue;
                        result.AchValue = achievedValueTimeSpan.ToString(@"HH\:mm");
                        result.AchPercentage = (
                            (achievedValueTimeSpan.Hours * 60 + achievedValueTimeSpan.Minutes)
                            / (matchTimeSpanValue.Hours * 60 + matchTimeSpanValue.Minutes) * 100
                        );
                        break;

                    case ComparisonOperator.LessThanOrEqualTo:
                        result.IsPassed = achievedValueTimeSpan <= matchTimeSpanValue;
                        result.AchValue = achievedValueTimeSpan.ToString(@"HH\:mm");
                        result.AchPercentage = (
                            (achievedValueTimeSpan.Hours * 60 + achievedValueTimeSpan.Minutes)
                                / (matchTimeSpanValue.Hours * 60 + matchTimeSpanValue.Minutes) * 100
                        );
                        break;

                    case ComparisonOperator.GreaterThanOrEqualTo:
                        result.IsPassed = achievedValueTimeSpan >= matchTimeSpanValue;
                        result.AchValue = achievedValueTimeSpan.ToString(@"HH\:mm");
                        result.AchPercentage = (
                            (achievedValueTimeSpan.Hours * 60 + achievedValueTimeSpan.Minutes)
                                / (matchTimeSpanValue.Hours * 60 + matchTimeSpanValue.Minutes) * 100
                         );
                        break;

                    case ComparisonOperator.GreaterThan:
                        result.IsPassed = achievedValueTimeSpan > matchTimeSpanValue;
                        result.AchValue = achievedValueTimeSpan.ToString(@"HH\:mm");
                        result.AchPercentage = (
                            (achievedValueTimeSpan.Hours * 60 + achievedValueTimeSpan.Minutes)
                                / (matchTimeSpanValue.Hours * 60 + matchTimeSpanValue.Minutes) * 100
                         );
                        break;

                    case ComparisonOperator.NotEqualTo:
                        result.IsPassed = achievedValueTimeSpan != matchTimeSpanValue;
                        result.AchValue = achievedValueTimeSpan.ToString(@"HH\:mm");
                        result.AchPercentage = (
                          (achievedValueTimeSpan.Hours * 60 + achievedValueTimeSpan.Minutes)
                             / (matchTimeSpanValue.Hours * 60 + matchTimeSpanValue.Minutes) * 100
                         );
                        break;
                }

                break;

            case KpiMeasure.ValueAPI:
                string achievedValueString = string.Empty;
                switch (kpi.Relation)
                {
                    case Relation.UseDirectReportDb:
                        achievedValueString = reportAchValue;
                        break;

                    case Relation.UseDirectMasterDb:
                        achievedValueString = masterAchvalue;
                        break;

                    case Relation.UseDirectTransactionDb:
                        achievedValueString = transactionAchValue;
                        break;
                }

                switch (qualifier.ComparisionOperator)
                {
                    case ComparisonOperator.EqualTo:
                        result.IsPassed = achievedValueString == qualifier.Target;
                        result.AchValue = achievedValueString;
                        break;

                    case ComparisonOperator.NotEqualTo:
                        result.IsPassed = achievedValueString != qualifier.Target;
                        result.AchValue = achievedValueString;
                        break;
                }

                break;
        }

        return result;
    }
}