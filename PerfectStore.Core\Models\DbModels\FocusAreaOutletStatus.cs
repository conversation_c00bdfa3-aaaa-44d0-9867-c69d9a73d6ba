﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PerfectStore.Core.Models.DbModels
{
    public class FocusAreaOutletStatus
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public long OutletId { get; set; }

        public long FocusAreaId { get; set; }

        public bool IsCompleted { get; set; }

        public double CompletionRate { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }
    }
}
