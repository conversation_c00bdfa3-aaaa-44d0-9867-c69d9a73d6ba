﻿using PerfectStore.Core.Models.DbModels;

namespace PerfectStore.Core.Repositories.MasterRepositories
{
    public interface IProductTagRepository
    {
        Task<Dictionary<long, List<ProductSuggestedQtyDto>>> GetProductSuggestedQuantities(
            long productTagId,
            long companyId
        );

        Task<List<ProductTagSuggestion>> GetProductTagSuggestions(
            long companyId,
            long productTagId
        );

        Task<long> GetProductTagSuggestionCompletionCount(
            long companyId,
            long outletId,
            long productTagId
        );
    }
}
