﻿using Library.CommonHelpers;
using Library.SqlHelper;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Repositories.UnifyClickhouseRepositories;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace PerfectStore.DbStorage.Repositories.UnifyClickhouseRepositories;

public class ClickhouseKpiRepository(IClickHouseConnectionManager connectionManager) : IClickhouseKpiRepository
{
    public async Task<string?> GetClickhouseMetricValue(
        string query,
        long companyId,
        long entityId,
        FilterConstraintEntityType entityType,
        long? productDivisionId,
        DateTime startDate,
        DateTime endDate,
        int monthNumber,
        string? parameterValue = null)
    {
        var replaceSqlQuery = ParameterizedSqlQuery
            .GetReplaceSqlQueryForPattern(query, @"\[\[(\w+)\]\]");

        var paramsInQuery = ParameterizedSqlQuery
            .GetParametersInSqlQuery(replaceSqlQuery, @"@\b\S+?\b");


        var parameterListDict = new Dictionary<string, List<long>>();
        if (!string.IsNullOrEmpty(parameterValue))
        {
            var parameterList = JsonSerializer
                .Deserialize<List<ParameterValue>>(parameterValue)
                ?? new List<ParameterValue>();
            parameterListDict = parameterList
                .ToDictionary(p => p.ParameterName.ToLower(), p => p.ParameterValues);
        }

        string replacedQuery = Regex.Replace(
            replaceSqlQuery,
            @"@\b\S+?\b",
            match =>
            {
                var name = match.Value.ToLowerInvariant().TrimStart('@');
                return name switch
                {
                    "companyid" => companyId.ToString(),
                    "esmid" => entityType == FilterConstraintEntityType.User
                                          ? entityId.ToString()
                                          : "NULL",
                    "startdate" => startDate.ToString("yyyyMMdd"),
                    "enddate" => endDate.ToString("yyyyMMdd"),
                    "productDivisionId" => productDivisionId?.ToString() ?? "0",
                    "id" => (entityType == FilterConstraintEntityType.Outlet
                                          || entityType == FilterConstraintEntityType.Distributor 
                                          || entityType == FilterConstraintEntityType.User)
                                          ? entityId.ToString() :  "0", 
                    "monthnumber" => monthNumber.ToString(),
                    _ when parameterListDict.ContainsKey(name)
                                      => string.Join(",", parameterListDict[name]),
                    _ => "NULL"
                };
            });

        var connection = connectionManager.GetConnection();
        var clickHouseDataProvider = new ClickHouseDataProvider(connection);
        var dataTable = await clickHouseDataProvider.FetchDataAsync(replacedQuery, null);

        if (dataTable.Rows.Count > 0 && dataTable.Columns.Count > 0 && dataTable.Rows[0][0] != DBNull.Value)
        {
            return dataTable.Rows[0][0].ToString();
        }

        return null;
    }
}
