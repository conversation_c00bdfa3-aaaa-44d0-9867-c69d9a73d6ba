﻿<Project Sdk="Microsoft.NET.Sdk;Microsoft.NET.Sdk.Publish">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	<InvariantGlobalization>false</InvariantGlobalization>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AspNetCore.KeyVault" Version="1.0.6" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.1" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.Storage.Queues" Version="5.2.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
	<PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
	<PackageReference Include="jjm.one.Serilog.Sinks.SlackWebHook" Version="2.1.3" />
    <PackageReference Include="Sentry.Serilog" Version="5.5.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Lite_Library\Library.ConnectionStringParsor\Library.ConnectionStringParsor.csproj" />
    <ProjectReference Include="..\Lite_Library\Library.ResiliencyHelpers\Library.ResiliencyHelpers.csproj" />
    <ProjectReference Include="..\Lite_Library\Library.SlackService\Library.SlackService.csproj" />
    <ProjectReference Include="..\PerfectStore.Core\PerfectStore.Core.csproj" />
    <ProjectReference Include="..\PerfectStore.DbStorage\PerfectStore.DbStorage.csproj" />
  </ItemGroup>
</Project>