﻿using PerfectStore.Core.Models.DbModels;

namespace PerfectStore.Core.Repositories.MasterRepositories;

public interface IPerfectEntityRepository
{
    Task<List<PerfectEntityRule>> GetActivePerfectEntityRules(long companyId, DateTime date);

    Task<PerfectEntityRule?> GetPerfectEntityRule(long companyId, long ruleId);

    Task<PerfectEntityRuleCriteria?> GetPerfectEntityCriteriaByCriteriaId(
        long companyId,
        long criteriaId
    );

    Task<FilterConstraintDetail?> GetFilterConstraintDetail(
        long companyId,
        long filterConstraintId
    );

    Task<List<PerfectEntityRuleCriteria>> GetCriteriasForRule(long companyId, long ruleId);

    Task<List<PerfectCriteriaSlabDetail>> GetSlabsForCriteria(long companyId, long criteriaId);

    Task<List<PerfectEntityQualifier>> GetPerfectEntityQualifiers(
        long companyId,
        List<long> qualifierIds
    );

    Task<PerfectEntityRule?> GetPerfectEntityRulesWithAll(long companyId, long ruleId);
}
