﻿using System.Net;
using ClickHouse.Client.ADO;
using Library.ConnectionStringParsor;
using Library.SqlHelper;
using Library.StorageWriter.Reader_Writer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using PerfectStore.Core.Models;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.Core.Repositories.ReportRepositories;
using PerfectStore.Core.Repositories.TransactionRepositories;
using PerfectStore.Core.Repositories.UnifyClickhouseRepositories;
using PerfectStore.Core.Services;
using PerfectStore.Core.Utils.Helpers;
using PerfectStore.DbStorage.DbContexts;
using PerfectStore.DbStorage.Repositories.MasterRepositories;
using PerfectStore.DbStorage.Repositories.ReportRepositories;
using PerfectStore.DbStorage.Repositories.TransactionRepositories;
using PerfectStore.DbStorage.Repositories.UnifyClickhouseRepositories;

namespace PerfectEntityContinuousProcessor.Configurations
{
    public static class Dependencies
    {
        public static void SetUp(IConfiguration config, IServiceCollection services)
        {
            #region Connection-Strings
            var masterDbConnectionString = config.GetConnectionString("MasterDbConnectionString");
            var writableMasterDbConnectionString = config.GetConnectionString(
                "WritableMasterDbConnectionString"
            );
            var writableTransactionDbConnectionString = config.GetConnectionString(
                "WritableTransactionDbConnectionString"
            );
            var transactionDbConnectionString = config.GetConnectionString(
                "TransactionDbConnectionString"
            );
            var nsDataApiConnectionString = config.GetConnectionString("NSDataApiConnectionString");
            var masterStorageConnectionString = config.GetConnectionString(
                "MasterStorageConnectionString"
            );
            var storageConnectionString = config.GetConnectionString("StorageConnectionString");

            #endregion

            #region DbContexts
            //MasterDbContext
            services.AddDbContext<MasterDbContext>(options =>
                options.UseSqlServer(masterDbConnectionString, sqlOptions =>
                    sqlOptions.EnableRetryOnFailure())
            );

            //WritableMasterDbContext
            services.AddDbContext<WritableMasterDbContext>(options =>
                options.UseSqlServer(writableMasterDbConnectionString)
            );

            //WritableTransactionDbContext
            services.AddDbContext<WritableTransactionDbContext>(options =>
                options.UseSqlServer(writableTransactionDbConnectionString)
            );

            //readonly TransactionDbContext
            services.AddDbContext<TransactionDbContext>(options =>
                options.UseSqlServer(transactionDbConnectionString, sqlOptions =>
                  sqlOptions.EnableRetryOnFailure())
            );

            services.AddScoped(_ => new UnifyDbSqlDataReader(config.GetConnectionString("UnifyReportDbConnectionString")));

            services
                .AddHttpClient(ClickHouseSqldataReader.SuggestedHttpClientName)
                .ConfigurePrimaryHttpMessageHandler(_ => new HttpClientHandler
                {
                    AutomaticDecompression = (
                        DecompressionMethods.Deflate | DecompressionMethods.GZip
                    ),
                });

            var clickhouseConnectionString = config.GetConnectionString("UnifyClickHouseConnectionString");
            var readOnlyClickhouseConnectionString = config.GetConnectionString("ReadOnlyUnifyClickHouseConnectionString");

            services.TryAddSingleton<IClickHouseConnectionManager>(provider =>
            {
                return new ClickHouseConnectionManager(readOnlyClickhouseConnectionString, 20);
            });

            services.AddScoped<ClickHouseDataProvider>();
            services.AddScoped<ClickHouseConnection>();
            services.AddScoped(e => new UnifyDbClickhouseSqlDataReader(clickhouseConnectionString,
                e.GetRequiredService<IHttpClientFactory>(),
                commandTimeout: 30
            ));

            services.AddScoped(e => new ReadOnlyUnifyDbClickhouseSqlDataReader(readOnlyClickhouseConnectionString,
          e.GetRequiredService<IHttpClientFactory>(),
          commandTimeout: 30
      ));

            services.AddScoped(e => new MasterDbSqlDataReader(
                config.GetConnectionString("MasterDbConnectionString")
            ));

            services.AddScoped(e => new TransactionDbSqlDataReader(
                config.GetConnectionString("TransactionDbConnectionString")
            ));

            services.AddScoped(e => new ReportDbSqlDataReader(
                config.GetConnectionString("ReportDbConnectionString")
            ));


            #endregion

            #region repos
            services.AddScoped<ICompanySettingRepository, CompanySettingRepository>();
            services.AddScoped<IPerfectStoreRuleRepository, PerfectStoreRuleRepository>();
            services.AddScoped<ITaskManagementRepository, TaskManagmentRepository>();
            services.AddScoped<IProductTagRepository, ProductTagRepository>();
            services.AddScoped<
                ITaskAchievementAndRewardsRepository,
                TaskAchievementAndRewardsRepository
            >();
            services.AddScoped<IFocusAreaOutletStatusRepository, FocusAreaOutletStatusRepository>();
            services.AddScoped<ITargetModuleRepository, TargetModuleRepository>();
            services.AddScoped<
                IPerfectStoreOutletStatusRepository,
                PerfectStoreOutletStatusRepository
            >();
            services.AddScoped<IPerfectEntityContinuousService, PerfectEntityContinuousService>();
            services.AddScoped<IPerfectEntityRepository, PerfectEntityRepository>();
            services.AddScoped<ICompanyTargetRepository, CompanyTargetRepository>();
            services.AddScoped<IPerfectEntityRuleAnalyticClickhouseRepository, PerfectEntityRuleAnalyticClickhouseRepository>();
            services.AddScoped<IPerfectEntityTransactionalRepository, PerfectEntityTransactionalRepository>();
            services.AddScoped<IKpiRepository, KpiRepository>();
            services.AddScoped<IMasterKpiRepository, MasterKpiRepository>();
            services.AddScoped<IReportKpiRepostory, ReportKpiRepository>();
            services.AddScoped<ITransactionKpiRepository, TransactionKpiRepository>();
            services.AddScoped<IOutletMetricRepository, OutletMetricRepository>();
            services.AddScoped<ILocationRepository, LocationRepository>();
            services.AddScoped<IDistributorRepository, DistributorRepository>();
            services.AddScoped<IEmployeeRepository, EmployeeRepository>();
            services.AddScoped<IPositionCodeEntityMappingRepository, PositionCodeEntityMappingRepository>();
            services.AddScoped<IClickhouseKpiRepository, ClickhouseKpiRepository>();
            services.AddScoped<IProductRepository, ProductRepository>();
            services.AddScoped<IAttendanceRepository, AttendanceRepository>();
            services.AddScoped<INonFAInvoiceRepository, NonFAInvoiceRepository>();
            services.AddScoped<IDmsInvoiceRepository, DmsInvoiceRepository>();
            #endregion

            #region services
            services.AddScoped<IMTDService, MTDService>();
            services.AddScoped<ITargetAchievementService, TargetAchievementService>();
            services.AddScoped<KpiService>();
           
            #endregion

            #region infrastructure
            var reportApiConnection = ApiConnectionString.GetConnection(nsDataApiConnectionString);
            var reportApiBaseUrl = reportApiConnection.BaseUrl;
            var reportApiToken = reportApiConnection.AuthToken;
            var targetApiBaseUrl = config.GetValue<string>("AppSettings:TargetApiUrl");
            var targetApiToken = config.GetValue<string>("AppSettings:TargetAchApiToken");
            services.AddSingleton(s => new AppConfigSettings
            {
                reportApiBaseUrl = reportApiBaseUrl,
                reportApiToken = reportApiToken,
                targetApiBaseUrl = targetApiBaseUrl,
                targetApiToken = targetApiToken,
            });
            #endregion
        }
    }
}
