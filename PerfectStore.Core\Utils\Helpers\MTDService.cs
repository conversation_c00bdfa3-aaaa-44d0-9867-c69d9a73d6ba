﻿using Flurl.Http;
using Library.DateTimeHelpers;
using Library.FaExceptions;
using PerfectStore.Core.Models;

namespace PerfectStore.Core.Utils.Helpers
{
    public interface IMTDService
    {
        Task<FA_MTD_LMTD> GetDates(
            long compnayId,
            DateTime date,
            int yearStartMonth,
            bool includeToday = false
        );
    }

    public class MTDService(AppConfigSettings _appConfigSettings) : IMTDService
    {
        public async Task<FA_MTD_LMTD> GetDates(
            long compnayId,
            DateTime date,
            int yearStartMonth,
            bool includeToday = false
        )
        {
            var api =
                $"{_appConfigSettings.reportApiBaseUrl}api/MTDLMTDDate/GetMTDLMTD?companyId={compnayId}&today={date.ToString("MM/dd/yyyy")}&includeToday={includeToday}&yearStartMonth={yearStartMonth}";
            try
            {
                var resonse = await api.WithOAuthBearerToken(_appConfigSettings.reportApiToken)
                    .GetJsonAsync<FA_MTD_LMTD>();
                return resonse;
            }
            catch (FlurlHttpException ex)
            {
                // Handle HTTP errors
                var response = await ex.GetResponseStringAsync();
                throw new KnownIssueException(
                    $"Some error occurred at {api}, error: {response}",
                    ex
                );
            }
        }
    }
}
