﻿using Libraries.CommonEnums;
using Libraries.CommonModels;
using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.MasterRepositories;

public class EmployeeRepository(MasterDbContext db) : IEmployeeRepository
{
    private IQueryable<ClientEmployee> GetEmployeeQueryable(
        long companyId,
        bool isIncludeDeactiveUsers = false
    )
    {
        return isIncludeDeactiveUsers
            ? db.ClientEmployees.Where(e => e.CompanyId == companyId && !e.IsTrainingUser)
            : db.ClientEmployees.Where(e =>
                e.CompanyId == companyId && !e.IsDeactive && !e.IsTrainingUser
            );
    }

    private IQueryable<ClientEmployee> GetFieldUsersQueryable(
        long companyId,
        bool isIncludeDeactiveUsers = false
    )
    {
        return GetEmployeeQueryable(companyId, isIncludeDeactiveUsers)
            .Where(e => e.IsFieldAppuser)
            .OrderByDescending(e => e.Id);
    }

    private IQueryable<ClientEmployee> GetFieldUserUnderRoleQueryable(
        long companyId,
        List<long> userIds,
        PortalUserRole portalUserRole,
        bool includeInactive = false
    )
    {
        return GetFieldUsersQueryable(companyId, includeInactive)
            .Where(u =>
                (
                    userIds.Contains(u.Parent.Parent.Parent.Parent.Parent.Id)
                    && u.Parent.Parent.Parent.Parent.Parent.UserRole == portalUserRole
                )
                || (
                    userIds.Contains(u.Parent.Parent.Parent.Parent.Id)
                    && u.Parent.Parent.Parent.Parent.UserRole == portalUserRole
                )
                || (
                    userIds.Contains(u.Parent.Parent.Parent.Id)
                    && u.Parent.Parent.Parent.UserRole == portalUserRole
                )
                || (
                    userIds.Contains(u.Parent.Parent.Id)
                    && u.Parent.Parent.UserRole == portalUserRole
                )
                || (userIds.Contains(u.Parent.Id) && u.Parent.UserRole == portalUserRole)
                || (userIds.Contains(u.Id) && u.UserRole == portalUserRole)
                || (userIds.Contains(u.Id) && PortalUserRole.ClientEmployee == portalUserRole)
                || portalUserRole == PortalUserRole.CompanyAdmin
                || portalUserRole == PortalUserRole.CompanyExecutive
                || portalUserRole == PortalUserRole.AccountManager
                || portalUserRole == PortalUserRole.GlobalAdmin
            );
    }

    public async Task<List<EmployeeMinWithRank>> GetFieldUserIdsUnderManagerModel(
        long companyId,
        PortalUserRole userRole,
        List<long> userIds,
        EmployeeType? userType = null
    )
    {
        return await GetFieldUserUnderRoleQueryable(companyId, userIds, userRole)
            .Where(u => (userType.HasValue && u.UserType == userType) || !userType.HasValue)
            .Select(e => new EmployeeMinWithRank { Id = e.Id, UserRank = e.Rank })
            .ToListAsync();
    }

    public async Task<List<EmployeeMinWithType>> GetAllEmployees(long companyId)
    {
        return await db
            .ClientEmployees.Where(e => e.CompanyId == companyId && !e.IsDeactive)
            .Select(e => new EmployeeMinWithType
            {
                Id = e.Id,
                Name = e.Name,
                UserType = e.UserType,
            })
            .ToListAsync();
    }

    public async Task<List<EmployeeMinWithType>> GetAllEmployeesSRAndDSR(long companyId)
    {
        return await db.ClientEmployees
            .Where(e => e.CompanyId == companyId
                        && !e.IsDeactive && e.IsFieldAppuser
                        && (e.UserType == EmployeeType.SR || e.UserType == EmployeeType.DSR))
            .Select(e => new EmployeeMinWithType
            {
                Id = e.Id,
                Name = e.Name,
                UserType = e.UserType
            }).ToListAsync();
    }
}
