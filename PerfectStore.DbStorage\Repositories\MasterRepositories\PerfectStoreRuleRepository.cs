﻿using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.MasterRepositories
{
    public class PerfectStoreRuleRepository(MasterDbContext masterDbContext)
        : IPerfectStoreRuleRepository
    {
        public async Task<List<PerfectStoreRule>> GetActivePerfectStoreRules(
            long companyId,
            DateTime dateTime
        )
        {
            var data = await masterDbContext
                .PerfectStoreRules.Where(s =>
                    s.CompanyId == companyId
                    && !s.Deleted
                    && !s.IsDeactive
                    && s.StartDate <= dateTime
                    && (s.EffectiveEndDate >= dateTime || s.EffectiveEndDate == null)
                )
                .ToListAsync();
            return data;
        }
    }
}
