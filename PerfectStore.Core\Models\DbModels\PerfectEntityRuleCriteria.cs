﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations.Schema;

namespace PerfectStore.Core.Models.DbModels;

public class PerfectEntityRuleCriteria
{
    public long Id { get; set; }
    public long RuleId { get; set; }
    public TaskManagementFocusAreaType CriteriaType { get; set; }
    public long? CriteriaEntityId { get; set; }
    public required string Name { get; set; }
    public long CompanyId { get; set; }
    public bool IsDeactive { get; set; } = false;
    public bool Deleted { get; set; } = false;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public required string CreationContext { get; set; }
    public double Weightage { get; set; } = 0;
    public string? QualifierRelation { get; set; }
    public string? QualifierIds { get; set; }
    public long? ParentId { get; set; }
    public string? Cron { get; set; }
    public ICollection<PerfectCriteriaSlabDetail> SlabDetails { get; set; }
    [ForeignKey("RuleId")] 
    public PerfectEntityRule Rule { get; set; }
}


public class QualifierRelationModel
{
    public List<QualifierRelationModel>? QualifiersRelations { get; set; } 
    public List<long>? QualifierIds { get; set; } 
    public string Operation { get; set; } = "AND"; // Logical operation (AND/OR)
}
