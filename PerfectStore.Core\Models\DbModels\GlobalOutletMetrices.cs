﻿using Libraries.CommonEnums;

namespace PerfectStore.Core.Models.DbModels;

public class GlobalOutletMetrices
{
    public long Id { get; set; }
    public string? Name { get; set; }
    public string? Description { get; set; }
    public bool IsDeleted { get; set; }
    public string? TransactionSqlQuery { get; set; }
    public string? ReportSqlQuery { get; set; }
    public string? MasterSqlQuery { get; set; }
    public string? ClickHouseQuery { get; set; }
    public OutletMetricQueryRelation? QueriesRelation { get; set; }
    public DataTypeEnum? DataType { get; set; }
    public DateTime? LastUpdatedAt { get; set; }
    public DateTime? CreatedAt { get; set; }
    public string? CreationContext { get; set; }
    public string? ParameterReferences { get; set; }
}