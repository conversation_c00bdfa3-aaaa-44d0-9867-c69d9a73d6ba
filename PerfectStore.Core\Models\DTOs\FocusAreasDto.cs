﻿using Libraries.CommonEnums;

namespace PerfectStore.Core.Models.DTOs
{
    public class FocusAreasDto
    {
        public TaskManagementFocusAreaType TaskType { get; set; }
        public long? TaskEntityId { get; set; }
        public long? EntityId { get; set; }
        public long? TaskTarget { get; set; }
        public long CompanyId { get; set; }
        public long FocusAreaId {  get; set; }
        public long TaskId { get; set; }

        public long Achievement { get; set; }
    }
}
