﻿using PerfectStore.Core.Models.DTOs;

namespace PerfectStore.Core.Repositories.MasterRepositories;

public interface ICompanyTargetRepository
{
    Task<TargetAchievementDto> GetCompanyTargetsWithSubscriptions(
            long companyId,
            long outletId,
            long? productDivisionIds,
            long companyTargetSubscriptionId,
            DateTime startDate,
            DateTime endDate
        );
}
