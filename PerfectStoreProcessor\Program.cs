using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Azure.Identity;
using jjm.one.Serilog.Sinks.SlackWebHook;
using PerfectStoreProcessor.Configurations;
using Serilog;
using Serilog.Events;

var builder = new HostBuilder()
    .ConfigureAppConfiguration(
        (config) =>
        {
            var env = Environment.GetEnvironmentVariable("BuildEnvironment");
            config.AddJsonFile($"appsettings.{env}.json", optional: true, reloadOnChange: true);
            var keyVaultEndpoint = Environment.GetEnvironmentVariable("KEYVAULT_ENDPOINT");
            if (!string.IsNullOrEmpty(keyVaultEndpoint))
            {
                config.AddAzureKeyVault(new Uri(keyVaultEndpoint), new DefaultAzureCredential());
            }
        }
                              )
    .UseSerilog(
        (hostingContext, loggerConfiguration) =>
        {
            var slackUri =
                "*********************************************************************************";
            var env = hostingContext.Configuration.GetValue<string>("AppSettings:Deployment");

#if DEBUG
            loggerConfiguration.MinimumLevel.Information();
#else
                            if (env == "dev")
                                loggerConfiguration.MinimumLevel.Information();
                            else
                                loggerConfiguration.MinimumLevel.Warning();
#endif
            loggerConfiguration
                .Enrich.FromLogContext()
                .WriteTo.Console()
                .WriteTo.Sentry(o =>
                    {
                        o.Dsn = "http://<EMAIL>:8000/5";
                        o.Debug = false;  // Enable Sentry debugging logs
                        o.TracesSampleRate = 1.0; // Capture 100% of transactions
                        o.Environment = Environment.GetEnvironmentVariable("BuildEnvironment") ?? "Production"; // Set environment dynamically
                        // Debug and higher are stored as breadcrumbs (default is Information)
                        o.MinimumBreadcrumbLevel = LogEventLevel.Debug;
                        // Warning and higher is sent as event (default is Error)
                        o.MinimumEventLevel = LogEventLevel.Error;
                    })
                .WriteTo.Slack(
                slackWebHookUrl: slackUri,
                slackChannel: "perfectstoreprocessor",
                slackUsername: $"{env}-Perfect Store Processor Logger",
                slackEmojiIcon: ":radioactive_sign:",
                periodicBatchingSinkOptionsBatchSizeLimit: 1,
                periodicBatchingSinkOptionsPeriod: TimeSpan.FromMilliseconds(1000),
                periodicBatchingSinkOptionsQueueLimit: 10000,
                sinkRestrictedToMinimumLevel: LogEventLevel.Error
                                );
        }
               )
    .ConfigureWebJobs(b =>
    {
        b.AddAzureStorageQueues(c =>
        {
            c.BatchSize =
#if DEBUG
                8;
#else
                                12;
#endif
            c.MaxPollingInterval = TimeSpan.FromSeconds(8);
        });
    })
    .ConfigureServices(
        (context, services) =>
        {
            Dependencies.SetUp(context.Configuration, services);
        }
                      )
    .UseConsoleLifetime();
var host = builder.Build();
using (host)
{
    try
    {
        await host.RunAsync();
    }
    catch (Exception ex)
    {
        SentrySdk.CaptureException(ex);
        throw;
    }
}