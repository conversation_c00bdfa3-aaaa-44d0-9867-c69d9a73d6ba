using PerfectStore.Core.Models.DbModels.ClickhouseDbModels;
using PerfectStore.Core.Repositories.UnifyClickhouseRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.UnifyClickhouseRepositories;

public class PerfectEntityRuleAnalyticClickhouseRepository(
    UnifyDbClickhouseSqlDataReader _unifyDbClickhouseSqlDataReader
) : IPerfectEntityRuleAnalyticClickhouseRepository
{

    public async Task SavePerfectEntityRuleAnalyticsAsync(
        List<PerfectEntityRuleAnalytic> perfectEntityRuleAnalytics
    )
    {
        await _unifyDbClickhouseSqlDataReader.BulkCopyData(
            perfectEntityRuleAnalytics,
            "PerfectEntityRuleAnalytics"
        );
    }
}
