﻿using Libraries.CommonEnums;
using PerfectStore.Core.Helpers.QueueHandlers;
using PerfectStore.Core.Models.DbModels;
using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.Core.Repositories.TransactionRepositories;
using PerfectStore.Core.Utils.Helpers;

namespace PerfectStore.Core.Services
{
    public interface IFocusAreaService
    {
        Task ProcessQueue(FocusAreaQueueData data);
    }

    public class FocusAreaService(
        ITaskManagementRepository taskManagementRepository,
        IProductTagRepository productTagRepository,
        ITaskAchievementAndRewardsRepository taskAchievementAndRewardsRepository,
        ITargetModuleRepository targetModuleRepository,
        IFocusAreaOutletStatusRepository focusAreaOutletStatusRepository,
        MTDService mTDService,
        TargetAchievementService achievementService,
        PerfecStoreTagQueueHandler perfectStoreTagQueueH<PERSON><PERSON>,
        ICompanySettingRepository companySettingsRepository
    ) : IFocusAreaService
    {
        public async Task ProcessQueue(FocusAreaQueueData data)
        {
            var yearStartMonth = await companySettingsRepository.GetYearStartMonth(data.CompanyId);
            var timeZoneOffset = await companySettingsRepository.GetTimeZoneOffset(data.CompanyId);
            var todayDate = data.CurrentDate.Add(timeZoneOffset).Date;
            var mtdLmtd = await mTDService.GetDates(
                data.CompanyId,
                todayDate,
                yearStartMonth,
                true
            );
            var daysInMonth = DateTime.DaysInMonth(todayDate.Year, todayDate.Month);
            var startDate = mtdLmtd.MTD.StartDate;
            var endDate = data.IsRepeatable
                ? (daysInMonth > data.RepeatFrequency)
                    ? startDate.AddDays(data.RepeatFrequency)
                    : startDate.AddDays(data.RepeatFrequency - 1)
                : data.EffectiveEndDate ?? todayDate;
            var focusAreaOutletStatuses = new List<FocusAreaOutletStatus>();
            var focusAreas = await taskManagementRepository.GetTaskManagementFocusAreasWithTasks(
                data.CompanyId,
                data.RuleId
            );
            //var taskAchievementAndRewards = await taskAchievementAndRewardsRepository.GetTaskAcheivementAndRewards(data.CompanyId, startDate, endDate);
            //var taskAchievementAndRewardsDict = taskAchievementAndRewards.GroupBy(s => new { s.TaskId, s.TaskEntityId}).ToDictionary(gr => gr.Key, gr => gr.ToList());
            var focusAreasDict = focusAreas
                .GroupBy(s => s.FocusAreaId)
                .ToDictionary(gr => gr.Key, gr => gr.ToList());
            var focusAreaIds = focusAreas.Select(s => s.FocusAreaId).Distinct().ToList();

            if (focusAreaIds.Count > 0)
            {
                foreach (var focusAreaId in focusAreaIds)
                {
                    var taskManagementFocusAreas = focusAreasDict.GetValueOrDefault(focusAreaId);
                    if (taskManagementFocusAreas?.Count > 0)
                    {
                        var focusArea = taskManagementFocusAreas.FirstOrDefault();
                        var focusAreaType = focusArea?.TaskType;
                        if (focusArea is not null)
                        {
                            switch (focusAreaType)
                            {
                                case TaskManagementFocusAreaType.ProductTag:
                                case TaskManagementFocusAreaType.NumberBased:
                                    foreach (
                                        var taskManagementFocusArea in taskManagementFocusAreas
                                    )
                                    {
                                        var achievement = taskManagementFocusArea.Achievement;
                                        var target = taskManagementFocusArea.TaskTarget;
                                        var focusAreaOutletStatus = new FocusAreaOutletStatus
                                        {
                                            CompanyId = data.CompanyId,
                                            OutletId = taskManagementFocusArea.TaskEntityId.Value,
                                            FocusAreaId = focusAreaId,
                                            IsCompleted = achievement >= target,
                                            CompletionRate =
                                                target != 0
                                                    ? Math.Min(
                                                        Math.Round(
                                                            (achievement / (double)target * 100),
                                                            2
                                                        ),
                                                        100
                                                    )
                                                    : 0,
                                            CreatedAt = DateTime.UtcNow,
                                            CreationContext = "perfect-store-tag-processor",
                                            StartDate = startDate,
                                            EndDate = endDate,
                                            LastUpdatedAt = DateTime.UtcNow,
                                        };
                                        focusAreaOutletStatuses.Add(focusAreaOutletStatus);
                                    }
                                    break;
                                case TaskManagementFocusAreaType.TargetAchievementBased:
                                    // TODO: will do them later
                                    break;
                                case TaskManagementFocusAreaType.IRBased:
                                    // TODO: will do them later
                                    break;
                                case TaskManagementFocusAreaType.SurveyBased:
                                    // TODO: will do them later
                                    break;
                            }
                        }
                    }
                }

                if (focusAreaOutletStatuses.Count != 0)
                {
                    await focusAreaOutletStatusRepository.UpsertFocusAreaOutletStatus(
                        focusAreaOutletStatuses,
                        data.CompanyId,
                        startDate,
                        endDate
                    );
                }
            }

            await perfectStoreTagQueueHandler.AddToQueue(
                new PerfectStoreTagQueueData
                {
                    RuleId = data.RuleId,
                    CompanyId = data.CompanyId,
                    RepeatFrequency = data.RepeatFrequency,
                    StartDate = data.StartDate,
                    EffectiveEndDate = data.EffectiveEndDate,
                    IsRepeatable = data.IsRepeatable,
                    CurrentDate = data.CurrentDate,
                    FocusAreaIds = focusAreaIds,
                    YearStartMonth = yearStartMonth,
                    TimeZoneOffset = timeZoneOffset,
                }
            );
        }

        private async Task<long> GetCompletionForProductTag(
            long companyId,
            long productTagId,
            long outletId
        )
        {
            var completionCount = await productTagRepository.GetProductTagSuggestionCompletionCount(
                companyId,
                outletId,
                productTagId
            );
            return completionCount;
        }

        private async Task<TaskAchAndRewardCountDto> GetCompleteTaskAchievementAndReward(
            long companyId,
            long taskEntityId,
            long focusAreaId
        )
        {
            var tasks = await taskAchievementAndRewardsRepository.GetTaskAchievementAndReward(
                companyId,
                taskEntityId,
                focusAreaId
            );
            var response = new TaskAchAndRewardCountDto
            {
                Target = tasks.Count,
                Achieved = tasks.Count(s => s.IsCompleted),
            };
            return response;
        }

        private async Task<int> GetNumBasedTaskAchievementAndReward(
            long companyId,
            long taskEntityId,
            long focusAreaId
        )
        {
            var tasks = await taskAchievementAndRewardsRepository.GetRecentTaskAchievementAndReward(
                companyId,
                taskEntityId,
                focusAreaId
            );
            if (tasks == null)
            {
                return 0;
            }
            return tasks.IsCompleted ? 1 : 0;
        }

        private async Task<FlexibleTargetCountDto> GetFlexibleTargetAchievement(
            long companyId,
            long targetMasterId,
            long taskEntityId,
            DateTime startDate,
            DateTime endDate
        )
        {
            var targetDetails = await targetModuleRepository.GetTargetAchDetailsForOutlet(
                companyId,
                targetMasterId,
                taskEntityId,
                startDate,
                endDate
            );
            var achieved = 0;
            foreach (var trgt in targetDetails)
            {
                var totalTarget = await achievementService.GetSingleTargetAch(
                    companyId,
                    trgt,
                    startDate,
                    endDate
                );
                if (totalTarget != null && totalTarget.Achievement >= totalTarget.Target)
                {
                    achieved++;
                }
            }
            var taskAch = new FlexibleTargetCountDto
            {
                Target = targetDetails.Count,
                Achieved = achieved,
            };
            return taskAch;
        }
    }
}
