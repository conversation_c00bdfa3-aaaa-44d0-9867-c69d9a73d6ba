﻿using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.MasterRepositories;

public class ProductRepository(MasterDbSqlDataReader masterDbSqlDataReader) : IProductRepository
{
    public async Task<List<ProductDto>> GetProductWithMOQAndCategory(long companyId)
    {
        var query = $@"
            SELECT p.Id, p.MOQ, p.ProductDisplayCategoryId, p.ProductGroupID as ProductGroupId
            FROM FACompanyProducts as p
            WHERE CompanyId = {companyId} AND IsActive = 1";
        var result = await masterDbSqlDataReader.GetModelFromQueryAsync<ProductDto>(query);
        return result.ToList();
    }
}
