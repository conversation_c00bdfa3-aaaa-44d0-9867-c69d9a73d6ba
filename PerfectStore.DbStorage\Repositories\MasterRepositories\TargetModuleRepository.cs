﻿using Microsoft.EntityFrameworkCore;
using PerfectStore.Core.Models.DTOs;
using PerfectStore.Core.Repositories.MasterRepositories;
using PerfectStore.DbStorage.DbContexts;

namespace PerfectStore.DbStorage.Repositories.MasterRepositories
{
    public class TargetModuleRepository(MasterDbContext masterDbContext) : ITargetModuleRepository
    {
        public async Task<List<TargetAchievementDto>> GetTargetAchDetailsForOutlet(
            long companyId,
            long targetId,
            long outletId,
            DateTime startDate,
            DateTime endDate
        )
        {
            var allData = await (
                from cts in masterDbContext.CompanyTargetSubscriptions
                join tm in masterDbContext.TargetMaster on cts.TargetMasterId equals tm.Id
                join ct in masterDbContext.CompanyTargets
                    on cts.Id equals ct.CompanyTargetSubscriptionId
                where
                    cts.CompanyId == companyId
                    && !cts.IsDeactive
                    && cts.Id == targetId
                    && ct.EndDate >= endDate.Date
                    && ct.StartDate <= startDate.Date
                select new TargetAchievementDto()
                {
                    TargetId = cts.Id,
                    TargetStartPeriod = ct.StartDate,
                    TargetMasterId = tm.Id,
                    TargetEndPeriod = ct.EndDate,
                    AchievementQuery = tm.AchievementQuery,
                    Frequency = ct.Frequency,
                    AchievementDb = tm.AchievementDb,
                    TargetValue = ct.Target,
                    Hierarchy1Id = ct.Hierarchy1Id,
                    Hierarchy2Id = ct.Hierarchy2Id.HasValue ? ct.Hierarchy2Id : null,
                    Hierarchy3Id = ct.Hierarchy3Id.HasValue ? ct.Hierarchy3Id : null,
                    CompanyTargetId = ct.Id,
                }
            ).Distinct().ToListAsync();

            return allData
                .Where(x =>
                    x.Hierarchy1Id == outletId
                    || x.Hierarchy2Id == outletId
                    || x.Hierarchy3Id == outletId
                )
                .ToList();
        }
    }
}
